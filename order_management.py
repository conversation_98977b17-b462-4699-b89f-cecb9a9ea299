# order_management.py
from NorenRestApiPy.NorenApi import NorenApi

class ShoonyaApiPy(NorenApi):
    def __init__(self):
        NorenApi.__init__(self, host='https://api.shoonya.com/NorenWClientTP/', websocket='wss://api.shoonya.com/NorenWSTP/')

def getCurrentPriceBySymbolName(api, tradingSymbolName):
    """Fetch the quote for a given exchange and token."""
    getScriptToken = api.searchscrip(exchange='NSE',searchtext=tradingSymbolName)
    response = api.get_quotes(exchange='NSE', token=getScriptToken['values'][0].get('token'))
    currentPriceFromResponse = response.get('lp')
    return currentPriceFromResponse

def getSymbolNameFinvasia(api, tradingSymbolName):
    """Fetch the quote for a given exchange and token."""

    # Extract trading symbol name before "-" if it exists
    if "-" in tradingSymbolName:
        symbolName = tradingSymbolName.split("-")[0].strip()  # strip to remove any leading/trailing spaces
    else:
        symbolName = tradingSymbolName

    getScriptToken = api.searchscrip(exchange='NSE', searchtext=symbolName)
    
    # Check if 'values' key exists and is a list
    if 'values' in getScriptToken and isinstance(getScriptToken['values'], list):
        # Iterate through the list of results
        for script in getScriptToken['values']:
            # Check if the 'symname' matches the extracted symbol name
            if script.get('symname') == symbolName:
                # Found the correct symbol
                response = api.get_quotes(exchange='NSE', token=script.get('token'))
                symbolNameFromResponse = response.get('tsym')
                return symbolNameFromResponse

    # If no match is found, return None or raise an exception
    return None  # Or raise ValueError("Symbol not found")

def placeOrder(api, buy_or_sell, tradingsymbol, quantity, 
                product_type='C', exchange='NSE', discloseqty=0, 
                price_type='LMT', price=None, trigger_price=None, 
                retention='DAY', amo='NO', remarks=None):
    """Places an order.  Price is required, so setting to 0 if not provided for market orders."""
    if price is None:
        price = 0  # Set price to 0 for market orders

    return api.place_order(
        buy_or_sell=buy_or_sell,
        product_type=product_type,
        exchange=exchange,
        tradingsymbol=tradingsymbol,
        quantity=quantity,
        discloseqty=discloseqty,
        price_type=price_type,
        price=price,
        trigger_price=trigger_price,
        retention=retention,
        amo=amo,
        remarks=remarks
    )