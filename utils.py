import os
import json
import logging # Added logging

# Define the directory and file path
JSON_DIR = os.path.join(os.path.dirname(__file__), 'json')
CONFIG_FILE = os.path.join(JSON_DIR, 'config.json') # Changed variable name for clarity

# --- Default Configuration ---
# Default configuration for the application
DEFAULT_CONFIG = {
    "symbols": [],
    "sell_percentage": 2.1,
    "trailing_stop_loss_percentage": 0.1,
    "investment_amount_per_stock": 1000,
    "auto_buy_enabled": False,
    "deposit_amount": 50000.0,
    "buy_on_dip_enabled": False,
    "buy_on_dip_config": [
        {"threshold": 3, "percentage": 20.0},
        {"threshold": 5, "percentage": 20.0},
        {"threshold": 7, "percentage": 20.0},
        {"threshold": 10, "percentage": 20.0}
    ],
    "auto_sell_enabled": True,
    "auto_pilot_enabled": False
}

def ensure_dir_exists(directory):
    """Creates the directory if it doesn't exist."""
    if not os.path.exists(directory):
        try:
            os.makedirs(directory)
            logging.info(f"Created directory: {directory}")
        except OSError as e:
            logging.error(f"Error creating directory {directory}: {e}")

def load_config():
    """Loads the configuration from the JSON file, ensuring all keys exist."""
    ensure_dir_exists(JSON_DIR) # Make sure the json directory exists

    if not os.path.exists(CONFIG_FILE):
        logging.warning(f"Config file not found at {CONFIG_FILE}. Creating with defaults.")
        # Initialize file with potentially missing keys from the current default config
        initial_config = DEFAULT_CONFIG.copy()
        save_config(initial_config) # Create file with defaults
        return initial_config # Return a copy of the defaults

    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)

            # Handle older format where file might just contain a list of symbols
            if isinstance(config, list):
                logging.warning("Old config format detected (list). Converting to new dictionary format.")
                loaded_config = DEFAULT_CONFIG.copy()
                loaded_config['symbols'] = config
            elif isinstance(config, dict):
                loaded_config = config
            else:
                # Handle unexpected format
                 logging.error(f"Unexpected format in config file {CONFIG_FILE}. Using defaults.")
                 loaded_config = DEFAULT_CONFIG.copy() # Start fresh with defaults


            # Ensure all default keys exist in the loaded config using setdefault
            # This adds missing keys with their default values without overwriting existing ones
            config_updated = False
            for key, value in DEFAULT_CONFIG.items():
                if key not in loaded_config:
                     loaded_config[key] = value
                     config_updated = True
                     logging.info(f"Added missing default key '{key}' to config.")


            # Prune any keys that are not in the default config (optional, helps keep config clean)
            keys_to_remove = [key for key in loaded_config if key not in DEFAULT_CONFIG]
            if keys_to_remove:
                logging.warning(f"Removing unknown keys from config: {keys_to_remove}")
                for key in keys_to_remove:
                    del loaded_config[key]
                config_updated = True # Mark as updated if keys were removed

            # If keys were added or removed, save the updated config back to the file
            # This helps keep the file structure consistent over time
            if config_updated:
                 logging.info(f"Resaving config file {CONFIG_FILE} to ensure structure consistency.")
                 save_config(loaded_config)


            return loaded_config

    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON from {CONFIG_FILE}: {e}. Returning defaults.")
        return DEFAULT_CONFIG.copy()


def update_auto_sell_status(enable_auto_sell: bool):
    """Updates the 'auto_sell_enabled' status in the config file."""
    logging.info(f"Attempting to set auto_sell_enabled to: {enable_auto_sell}")
    config = load_config()
    if config.get('auto_sell_enabled') == enable_auto_sell:
        logging.info(f"auto_sell_enabled is already set to {enable_auto_sell}. No change needed.")
        return
    config['auto_sell_enabled'] = enable_auto_sell
    save_config(config)
    logging.info(f"Successfully set auto_sell_enabled to: {enable_auto_sell}")

def save_config(config):
    """Saves the configuration dictionary to the JSON file."""
    ensure_dir_exists(JSON_DIR) # Make sure the json directory exists
    try:
        # Ensure we are saving a dictionary
        if not isinstance(config, dict):
             logging.error(f"Attempted to save non-dictionary config: {type(config)}")
             return # Or raise an error

        # Ensure only known keys are saved by filtering based on DEFAULT_CONFIG
        # Now 'deposit_amount' WILL be included because it's in DEFAULT_CONFIG
        config_to_save = {key: config[key] for key in DEFAULT_CONFIG if key in config}

        # Check if any expected keys are missing from the input config before saving
        missing_keys = [key for key in DEFAULT_CONFIG if key not in config_to_save]
        if missing_keys:
             logging.warning(f"Saving config, but expected keys are missing: {missing_keys}. They might be added on next load.")

        with open(CONFIG_FILE, 'w') as f:
            json.dump(config_to_save, f, indent=4) # Use indent for readability
        logging.info(f"Configuration successfully saved to {CONFIG_FILE}") # Added confirmation log
    except IOError as e:
        logging.error(f"Error writing config file {CONFIG_FILE}: {e}", exc_info=True) # Added exc_info
    except Exception as e:
         logging.error(f"Unexpected error saving config to {CONFIG_FILE}: {e}", exc_info=True)


def round_to_two_decimal(value):
    """Helper function to round a number to two decimal places."""
    try:
        return round(float(value), 2)
    except (ValueError, TypeError):
        # logging.warning(f"Could not round value: {value}. Returning 0.0") # Optional warning
        return 0.0 # Return a default float value on error

def is_valid_symbol(symbol):
    """Check if the symbol is valid (does not contain common exclusions and is not purely numeric)."""
    if not isinstance(symbol, str):
        return False
    # Simplified exclusion check
    exclusions = ["BEES", "ETF", "ALPHA", "MAFANG", "MOREALTY", "MOMENTUM", "LIC", "GOLD", "NIF", "SIL", "LIQ"]
    # Check if symbol ends with '-EQ' or other common suffixes first, then check base
    base_symbol = symbol.split('-')[0]
    if any(excl in base_symbol.upper() for excl in exclusions):
        return False
    # Check if the base symbol contains only digits (e.g., '20MICRONS') - Allow this for now, refine if needed
    # if base_symbol.isdigit():
    #    return False
    return True

def clean_symbol(symbol):
    """Remove 'NSE:' prefix from the symbol if present."""
    if isinstance(symbol, str) and symbol.startswith("NSE:"):
        return symbol[4:]
    return symbol

def calculate_percentage_change(cost_price, ltp):
    """Calculate the percentage change based on cost price and last traded price."""
    try:
        cost_price_float = float(cost_price)
        ltp_float = float(ltp)
        if cost_price_float == 0:
            return 0.0  # Avoid division by zero
        change = ((ltp_float - cost_price_float) / cost_price_float) * 100
        return round_to_two_decimal(change)
    except (ValueError, TypeError):
        # logging.warning(f"Could not calculate percentage change for cost: {cost_price}, ltp: {ltp}. Returning 0.0") # Optional
        return 0.0