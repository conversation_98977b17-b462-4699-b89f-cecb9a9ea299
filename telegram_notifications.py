import os
import json
import requests
from datetime import datetime, date, timezone
import logging

from order_management import getCurrentPriceBySymbolName
from trade_history import load_trade_history 
import credentials as cr

TELEGRAM_BOT_TOKEN = cr.TELEGRAM_BOT_TOKEN
TELEGRAM_CHAT_ID = cr.TELEGRAM_CHAT_ID

PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
JSON_DIR = os.path.join(PROJECT_ROOT, 'json')
TRADE_HISTORY_FILE = os.path.join(JSON_DIR, 'trade_history.json')

def load_trade_history_local():
    """Loads trade history from the JSON file."""
    try:
        if not os.path.exists(TRADE_HISTORY_FILE):
            logging.warning(f"Trade history file not found: {TRADE_HISTORY_FILE}. Returning empty list.")
            if not os.path.exists(JSON_DIR):
                os.makedirs(JSON_DIR)
                logging.info(f"Created directory: {JSON_DIR}")
            with open(TRADE_HISTORY_FILE, 'w') as f:
                json.dump([], f)
            return []
            
        with open(TRADE_HISTORY_FILE, 'r') as f:
            history = json.load(f)
            return history if isinstance(history, list) else []
    except json.JSONDecodeError:
        logging.error(f"Error decoding trade history file: {TRADE_HISTORY_FILE}. Returning empty list.")
        return []
    except Exception as e:
        logging.error(f"An unexpected error occurred while loading trade history: {e}")
        return []

# --- Telegram Notification Logic ---

def send_telegram_message(message):
    """Send a message to the specified Telegram chat."""
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHAT_ID:
        logging.error("Telegram bot token or chat ID is missing from credentials.py.")
        return

    url_telegram = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    payload = {
        "chat_id": TELEGRAM_CHAT_ID,
        "text": message,
        "parse_mode": "Markdown"
    }
    try:
        response = requests.post(url_telegram, json=payload, timeout=10)
        response.raise_for_status() 
        logging.info("Telegram message sent successfully.")
    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to send Telegram message: {e}")

def get_daily_summary_data(api_instance):
    """Gathers data for the daily summary."""
    summary = {
        "auto_buys_today": [],
        "buy_on_dip_today": [],
        "manual_buys_today": [],
        "auto_sells_today": [],
        "manual_sells_today": [],
        "total_buy_value_today": 0.0,
        "total_sell_value_today": 0.0,
        "net_trade_value_today": 0.0,
        "total_profit_loss_today": 0.0,
        "active_investment_value": 0.0,
        "errors": []
    }
    today = date.today()

    trade_history_list = load_trade_history()
    today_str = today.isoformat()

    for trade in trade_history_list:
        try:
            symbol = trade.get('symbol', 'N/A')
            total_quantity = float(trade.get('total_quantity', 0))

            buy_date_str = trade.get('buy_date')
            if buy_date_str and isinstance(buy_date_str, str):
                try:
                    buy_dt = datetime.fromisoformat(buy_date_str)
                    if buy_dt.date() == today:
                        buy_price_avg = float(trade.get('buy_price_avg', 0))
                        buy_value = total_quantity * buy_price_avg
                        buy_context = trade.get('buy_context', 'Unknown Buy')
                        
                        # Categorize buys by context
                        buy_entry = f"{symbol}: {total_quantity} @ ₹{buy_price_avg:.2f}"
                        if 'Auto Buy' in buy_context:
                            summary['auto_buys_today'].append(buy_entry)
                        elif 'Buy on Dip' in buy_context:
                            summary['buy_on_dip_today'].append(buy_entry)
                        elif 'Manual Buy' in buy_context:
                            summary['manual_buys_today'].append(buy_entry)
                        else:
                            summary['manual_buys_today'].append(f"{buy_entry} ({buy_context})")
                        
                        summary['total_buy_value_today'] += buy_value
                except ValueError as ve:
                    logging.warning(f"Could not parse buy_date '{buy_date_str}' for {symbol}: {ve}")

            # Check for sells today
            sell_date_str = trade.get('sell_date')
            if sell_date_str and isinstance(sell_date_str, str):
                try:
                    sell_dt = datetime.fromisoformat(sell_date_str)
                    if sell_dt.date() == today:
                        sell_price = float(trade.get('sell_price', 0))
                        buy_price_avg = float(trade.get('buy_price_avg', 0))
                        sell_value = total_quantity * sell_price
                        profit_loss = (sell_price - buy_price_avg) * total_quantity
                        sell_context = trade.get('sell_context', 'Manual Sell')
                        
                        # Categorize sells by context
                        sell_entry = f"{symbol}: {total_quantity} @ ₹{sell_price:.2f} (P/L: ₹{profit_loss:.2f})"
                        if 'Auto Sell' in sell_context or 'Auto-Sell' in sell_context:
                            summary['auto_sells_today'].append(sell_entry)
                        else:
                            summary['manual_sells_today'].append(sell_entry)
                        
                        summary['total_sell_value_today'] += sell_value
                        summary['total_profit_loss_today'] += profit_loss
                except ValueError as ve:
                    logging.warning(f"Could not parse sell_date '{sell_date_str}' for {symbol}: {ve}")

        except Exception as e:
            logging.error(f"Error processing trade for summary: {trade}. Error: {e}")
            summary['errors'].append(f"Error processing one trade for summary: {e}")

    summary['net_trade_value_today'] = summary['total_sell_value_today'] - summary['total_buy_value_today']

    # --- Consolidated Active Investment Calculation ---
    summary['active_investment_value'] = 0.0  # Reset for new calculation method
    consolidated_holdings = {}  # Stores {'symbol': {'quantity': X, 'cost_basis_fallback': Y, 'name': Z}}

    # 1. Process API Holdings (Primary Source for Settled Quantity)
    if api_instance:
        try:
            holdings_response = api_instance.get_holdings()
            if holdings_response:
                for holding in holdings_response:
                    if not isinstance(holding, dict) or holding.get('stat') != 'Ok':
                        continue
                    
                    trading_symbol = holding.get('tsym')
                    # Attempt to get a descriptive name, fallback to 'N/A'
                    holding_name = holding.get('stkVal', {}).get('name', holding.get('dname', 'N/A')) 
                    if not trading_symbol: 
                        if 'exch_tsym' in holding and isinstance(holding['exch_tsym'], list):
                            for tsym_info in holding['exch_tsym']:
                                if isinstance(tsym_info, dict) and tsym_info.get('exch') == 'NSE':
                                    trading_symbol = tsym_info.get('tsym')
                                    # If name wasn't found from stkVal or dname, try from exch_tsym
                                    if holding_name == 'N/A': 
                                        holding_name = tsym_info.get('dname', 'N/A')
                                    break
                    if not trading_symbol:
                        logging.warning(f"Could not determine trading symbol for API holding: {holding.get('tkn', 'Unknown Token')}")
                        continue

                    quantity = int(holding.get('holdqty', 0))
                    if quantity > 0:
                        if trading_symbol not in consolidated_holdings:
                            consolidated_holdings[trading_symbol] = {'quantity': 0, 'cost_basis_fallback': None, 'name': 'N/A'}
                        
                        consolidated_holdings[trading_symbol]['quantity'] += quantity
                        # Prefer API name if available and current is 'N/A'
                        if consolidated_holdings[trading_symbol]['name'] == 'N/A' and holding_name != 'N/A':
                            consolidated_holdings[trading_symbol]['name'] = holding_name
            else:
                logging.info("No holdings data received from API for active investment calculation.")
        except Exception as e:
            err_msg = f"Error fetching or processing API holdings: {e}"
            logging.error(err_msg, exc_info=True)
            summary['errors'].append(err_msg)
    else:
        err_msg = "API instance not provided for fetching API holdings (Step 1)."
        logging.warning(err_msg)
        summary['errors'].append(err_msg)

    # 2. Process Open Positions from Trade History (for Cost Basis, Unsettled/Recent Buys)
    for trade in trade_history_list:
        try:
            trade_symbol = trade.get('symbol')
            if not trade_symbol or trade.get('sell_price') is not None:  # Skip if no symbol or already sold
                continue

            trade_quantity = float(trade.get('total_quantity', 0))
            buy_price_avg = float(trade.get('buy_price_avg', 0))
            trade_name = trade.get('name', 'N/A') # Get name from trade history

            if trade_symbol not in consolidated_holdings:
                # This is an open position from history not found in current API holdings (e.g., bought today, API sync issue)
                consolidated_holdings[trade_symbol] = {
                    'quantity': trade_quantity, 
                    'cost_basis_fallback': buy_price_avg, 
                    'name': trade_name
                }
                logging.info(f"Added {trade_symbol} from trade_history (not in API holdings) to consolidated: Qty={trade_quantity}")
            else:
                # Stock is in API holdings, and also has an open entry in trade history.
                # Use trade_history to provide cost_basis_fallback if not already set or to update name.
                # Quantity is primarily driven by API; trade_history quantity for *open positions* might reflect unsettled buys.
                # If trade_history 'total_quantity' for an *open* position is different from API, it could indicate a discrepancy
                # or a very recent transaction. For now, we assume API quantity is the baseline for *settled* shares.
                # We will use the buy_price_avg from this trade as a potential cost_basis_fallback.
                if consolidated_holdings[trade_symbol].get('cost_basis_fallback') is None:
                    consolidated_holdings[trade_symbol]['cost_basis_fallback'] = buy_price_avg
                
                # If trade_history name is more descriptive than current consolidated name (often 'N/A' from API)
                if trade_name != 'N/A' and (consolidated_holdings[trade_symbol]['name'] == 'N/A' or consolidated_holdings[trade_symbol]['name'] is None):
                    consolidated_holdings[trade_symbol]['name'] = trade_name

        except Exception as e:
            logging.error(f"Error processing trade_history item for consolidated holdings: {trade}. Error: {e}")
            summary['errors'].append(f"Error during trade_history consolidation: {e}")

    # 3. Calculate Total Active Investment Value from Consolidated Holdings
    for symbol, data in consolidated_holdings.items():
        quantity = data['quantity']
        cost_basis_fallback = data['cost_basis_fallback']
        # name = data['name'] # Name is available if needed for logging here
        
        if quantity <= 0:
            continue

        current_price = None
        value_to_add = 0

        if api_instance:
            current_price_str = getCurrentPriceBySymbolName(api_instance, symbol)
            if current_price_str:
                try:
                    current_price = float(current_price_str)
                except ValueError:
                    logging.warning(f"Could not parse current price '{current_price_str}' for {symbol}. Will attempt fallback.")
                    summary['errors'].append(f"Invalid price format for {symbol}: {current_price_str}")
        
        if current_price is not None:
            value_to_add = quantity * current_price
            logging.info(f"Valued {symbol}: {quantity} @ {current_price:.2f} (current market price) = {value_to_add:.2f}")
        elif cost_basis_fallback is not None:
            value_to_add = quantity * cost_basis_fallback
            logging.warning(f"Could not fetch current price for {symbol}. Using cost basis fallback: {quantity} @ {cost_basis_fallback:.2f} = {value_to_add:.2f}")
            summary['errors'].append(f"Used cost basis for {symbol} as current price unavailable.")
        else:
            logging.error(f"Could not determine value for {symbol} ({quantity} shares): No current price and no cost basis fallback.")
            summary['errors'].append(f"Could not value {symbol}; price and cost basis unavailable.")
            continue # Skip adding to active investment if no price information
        
        summary['active_investment_value'] += value_to_add
    logging.info(f"Final calculated active_investment_value: {summary['active_investment_value']:.2f}")
        
    return summary

def format_summary_message(summary_data):
    """Formats the summary data into a readable Telegram message."""
    today_str = date.today().strftime('%Y-%m-%d') 
    message_parts = [f"📊 *Finvasia Trading Report ({today_str})*"]

    # === BUY TRANSACTIONS ===
    total_buy_transactions = (len(summary_data['auto_buys_today']) + 
                             len(summary_data['buy_on_dip_today']) + 
                             len(summary_data['manual_buys_today']))
    
    if total_buy_transactions > 0:
        message_parts.append("\n🛍️ *BUY TRANSACTIONS:*")
        
        # Auto Buy transactions
        if summary_data['auto_buys_today']:
            message_parts.append("\n🤖 _Auto Buy (3:15 PM):_")
            message_parts.extend([f"  ✅ {buy}" for buy in summary_data['auto_buys_today']])
        
        # Buy on Dip transactions
        if summary_data['buy_on_dip_today']:
            message_parts.append("\n📉 _Buy on Dip:_")
            message_parts.extend([f"  ✅ {buy}" for buy in summary_data['buy_on_dip_today']])
        
        # Manual Buy transactions
        if summary_data['manual_buys_today']:
            message_parts.append("\n👤 _Manual Buy:_")
            message_parts.extend([f"  ✅ {buy}" for buy in summary_data['manual_buys_today']])
        
        message_parts.append(f"\n💸 *Total Buy Value: ₹{summary_data['total_buy_value_today']:.2f}*")
    else:
        message_parts.append("\n🛍️ *BUY TRANSACTIONS:* None today")

    # === SELL TRANSACTIONS ===
    total_sell_transactions = (len(summary_data['auto_sells_today']) + 
                              len(summary_data['manual_sells_today']))
    
    if total_sell_transactions > 0:
        message_parts.append("\n🏷️ *SELL TRANSACTIONS:*")
        
        # Auto Sell transactions
        if summary_data['auto_sells_today']:
            message_parts.append("\n⚡ _Auto Sell (Stop-Loss):_")
            message_parts.extend([f"  ☑️ {sell}" for sell in summary_data['auto_sells_today']])
        
        # Manual Sell transactions
        if summary_data['manual_sells_today']:
            message_parts.append("\n👤 _Manual Sell:_")
            message_parts.extend([f"  ☑️ {sell}" for sell in summary_data['manual_sells_today']])
        
        message_parts.append(f"\n🤑 *Total Sell Value: ₹{summary_data['total_sell_value_today']:.2f}*")
    else:
        message_parts.append("\n🏷️ *SELL TRANSACTIONS:* None today")

    # === SUMMARY METRICS ===
    message_parts.append("\n📈 *DAILY SUMMARY:*")
    message_parts.append(f"🎯 Total P/L Today: ₹{summary_data['total_profit_loss_today']:.2f}")
    message_parts.append(f"🏦 Total Active Investment: ₹{summary_data['active_investment_value']:.2f}")
    
    # Calculate net flow
    net_flow = summary_data['total_sell_value_today'] - summary_data['total_buy_value_today']
    flow_emoji = "💰" if net_flow > 0 else "💸" if net_flow < 0 else "⚖️"
    message_parts.append(f"{flow_emoji} Net Cash Flow: ₹{net_flow:.2f}")

    # === NOTICES/ERRORS ===
    if summary_data['errors']:
        message_parts.append("\n⚠️ *NOTICES/ERRORS:*")
        message_parts.extend([f"  ❗ {error}" for error in summary_data['errors']])
        
    return "\n".join(message_parts)
