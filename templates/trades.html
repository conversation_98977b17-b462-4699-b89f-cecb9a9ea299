<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade History - Finvasia</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.5/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='Breakout.ico') }}">

    <style>
        /* --- Copied styles from index.html --- */
        :root {
            --primary-rgb: 99, 102, 241;
            --primary: #6366f1;
            --primary-hover: #4f46e5;
            --success: #22c55e;
            --success-hover: #16a34a;
            --warning: #f59e0b;
            --danger: #ef4444;
            --danger-hover: #dc2626;
            --info: #3abff8;
            --muted: #64748b;
            --light: #f8fafc;
            --dark: #0f172a;
            --body-bg: var(--light);
            --body-color: #334155;
            --card-bg: #ffffff;
            --card-border: #e2e8f0;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.03), 0 1px 2px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.08);
            --table-header-bg: #f1f5f9;
            --table-row-hover-bg: #f8fafc;
            --input-bg: #ffffff;
            --input-border: #cbd5e1;
            --input-focus-border: var(--primary);
            --input-focus-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), .25);
            --skeleton-bg: #e2e8f0;
            --skeleton-animation: skeleton-loading 1.5s infinite ease-in-out;
            --border-radius: 0.75rem;
            --transition-speed: 0.2s;
            --transition-ease: ease-in-out;
            --chart-grid-color: var(--card-border);
            --chart-tick-color: var(--muted);
            --chart-label-color: var(--muted);
        }

        [data-bs-theme="dark"] {
            --muted: #94a3b8;
            --body-bg: var(--dark);
            --body-color: #cbd5e1;
            --card-bg: #1e293b;
            --card-border: #334155;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.2);
            --card-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.25);
            --table-header-bg: #293548;
            --table-row-hover-bg: #243147;
            --input-bg: #293548;
            --input-border: #475569;
            --skeleton-bg: #334155;
            --chart-grid-color: var(--card-border);
            --chart-tick-color: var(--muted);
            --chart-label-color: var(--muted);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--body-bg);
            color: var(--body-color);
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease);
            font-size: 0.95rem;
        }

        .card, .stat-card, .summary-card, .stock-performance-card {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease), background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
            margin-bottom: 1.5rem;
        }

        .card:hover, .stat-card:hover, .summary-card:hover, .stock-performance-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--card-shadow-hover);
        }
        .card-body.p-0 {
             padding: 0 !important;
        }
        .card-header {
            background-color: var(--table-header-bg);
            border-bottom: 1px solid var(--card-border);
            padding: 0.85rem 1.25rem;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            transition: background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .card-header h6 { font-weight: 600; margin-bottom: 0; }

        .stat-card {
            padding: 1.25rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 115px;
            overflow: hidden;
        }
         .stat-card .stat-label {
             color: var(--muted);
             font-size: 0.85rem;
             margin-bottom: 0.25rem;
             line-height: 1.3;
             font-weight: 500;
        }
        .stat-card .stat-value {
            font-size: 1.6rem; /* MODIFIED: Reduced from 1.75rem */
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 0.25rem;
            word-break: break-word;
        }
        .stat-card .stat-subtext {
            font-size: 0.8rem;
            color: var(--muted);
            line-height: 1.3;
        }
        .stat-card .stat-subtext .fw-medium {
            font-weight: 500 !important;
            color: var(--body-color);
        }
        .stat-card .stat-subtext .profit-positive { color: var(--success) !important; }
        .stat-card .stat-subtext .profit-negative { color: var(--danger) !important; }
        #net-available-card.emphasized {
            border-left: 4px solid var(--primary);
        }


        .summary-card, .stock-performance-card {
             padding: 1.5rem;
        }

        .profit-positive { color: var(--success); }
        .profit-negative { color: var(--danger); }
        .metric-small { font-size: 0.85em; color: var(--muted); }

        .table-responsive-custom {
            overflow-x: auto;
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            transition: border-color var(--transition-speed) var(--transition-ease);
            margin: -1px;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
         .card-body > .table-responsive-custom:first-child {
             border-top-left-radius: 0;
             border-top-right-radius: 0;
         }
         .card:not(:has(.card-header)) .card-body > .table-responsive-custom:first-child {
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
         }


        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            min-width: 950px;
        }

        .table thead th {
            background-color: var(--table-header-bg);
            border-bottom: 2px solid var(--card-border);
            color: var(--body-color);
            font-weight: 600;
            padding: 0.85rem 0.75rem;
            white-space: nowrap;
            vertical-align: middle;
            transition: background-color var(--transition-speed) var(--transition-ease);
            position: sticky;
            top: 0;
            z-index: 1;
            cursor: pointer;
            user-select: none;
        }

         .table tbody tr {
            transition: background-color var(--transition-speed) var(--transition-ease), opacity 0.3s var(--transition-ease), border-left-color var(--transition-speed) var(--transition-ease);
            opacity: 1;
            border-left: 3px solid transparent;
         }
         .table tbody tr.trade-row-open { border-left-color: var(--warning); }
         .table tbody tr.trade-row-closed { border-left-color: var(--success); }


        .table-hover tbody tr:hover {
            background-color: var(--table-row-hover-bg);
        }

        .table td {
            padding: 0.85rem 0.75rem;
            vertical-align: middle;
            border-top: 1px solid var(--card-border);
            transition: border-color var(--transition-speed) var(--transition-ease);
            white-space: nowrap;
        }
        .table tbody tr:last-child td:first-child { border-bottom-left-radius: var(--border-radius); }
        .table tbody tr:last-child td:last-child { border-bottom-right-radius: var(--border-radius); }

        th.asc::after, th.desc::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            border: 4px solid transparent;
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
        th.asc::after { border-bottom-color: var(--primary); }
        th.desc::after { border-top-color: var(--primary); }

        th:not(:first-child), td:not(:first-child) { text-align: center; }
        th:first-child, td:first-child { text-align: left; padding-left: 1rem; }
        th:last-child, td:last-child { padding-right: 1rem; }
        td.actions-cell { text-align: center !important; padding-left: 0.5rem; padding-right: 0.5rem;}
        td.actions-cell .btn { opacity: 0.7; }
        .table tbody tr:hover td.actions-cell .btn { opacity: 1; }


        .status-badge {
            padding: 0.25rem 0.6rem;
            border-radius: 1rem;
            font-size: 0.8em;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            border: 1px solid transparent;
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .status-badge.bg-success-soft { background-color: rgba(34, 197, 94, 0.1); color: var(--success); border-color: rgba(34, 197, 94, 0.2); }
        .status-badge.bg-warning-soft { background-color: rgba(245, 158, 11, 0.1); color: var(--warning); border-color: rgba(245, 158, 11, 0.2); }

        .stock-badge {
            background: rgba(var(--primary-rgb), 0.1);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid rgba(var(--primary-rgb), 0.2);
            text-decoration: none;
            display: inline-block;
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .stock-badge:hover { background: rgba(var(--primary-rgb), 0.15); color: var(--primary-hover); }

        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all var(--transition-speed) var(--transition-ease);
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            display: inline-flex; align-items: center; justify-content: center; gap: 0.4rem;
            border: 1px solid transparent;
            line-height: 1.5;
            min-height: calc(1.5em + 1rem + 2px);
        }
        .btn:hover { transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0,0,0,0.08); }
        .btn i { vertical-align: middle; line-height: 1; }
        .btn span { vertical-align: middle; }
        .btn-primary { background-color: var(--primary); border-color: var(--primary); color: white; }
        .btn-primary:hover { background-color: var(--primary-hover); border-color: var(--primary-hover); }
        .btn-outline-secondary { border-color: var(--card-border); color: var(--muted); }
        .btn-outline-secondary:hover { background-color: var(--table-row-hover-bg); border-color: var(--card-border); color: var(--body-color); }
        .btn-outline-primary { color: var(--primary); border-color: var(--primary); }
        .btn-outline-primary:hover, .btn-outline-primary.active { background-color: rgba(var(--primary-rgb), 0.1); color: var(--primary); }
        .btn-outline-primary.active { background-color: var(--primary); color: white; }
        .btn-sm { padding: 0.35rem 0.8rem; font-size: 0.875rem; gap: 0.3rem; min-height: calc(1.5em + 0.7rem + 2px); }
        .btn-group .btn { border-radius: 0; margin-left: -1px; }
        .btn-group .btn:first-child { margin-left: 0; border-top-left-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }
        .btn-group .btn:last-child { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }

        .date-range-picker input,
        #deposit-amount-input,
        #symbol-search-input {
            border: 1px solid var(--input-border);
            background-color: var(--input-bg);
            color: var(--body-color);
            border-radius: 0.5rem;
            padding: 0.45rem 0.75rem;
            transition: border-color var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease), background-color var(--transition-speed) var(--transition-ease);
            max-width: 220px;
        }
        .date-range-picker input:focus,
        #deposit-amount-input:focus,
        #symbol-search-input:focus {
            border-color: var(--input-focus-border);
            outline: 0;
            box-shadow: var(--input-focus-shadow);
        }
        #deposit-amount-input {
            max-width: 150px;
        }
        #symbol-search-input {
            max-width: 200px;
        }
        .control-label {
            color: var(--muted);
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
            display: block;
        }


        .flatpickr-calendar { box-shadow: var(--card-shadow-hover); border-radius: var(--border-radius); border: 1px solid var(--card-border); background: var(--card-bg); }
        [data-bs-theme="dark"] .flatpickr-calendar { background: var(--card-bg) !important; border-color: var(--card-border) !important; }
        .flatpickr-day { color: var(--body-color); }
        [data-bs-theme="dark"] .flatpickr-day { color: var(--body-color); }
        .flatpickr-day:hover, .flatpickr-day:focus { background: var(--table-row-hover-bg); border-color: var(--table-row-hover-bg); color: var(--body-color); }
        .flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange { background: var(--primary); border-color: var(--primary); color: white; }
        [data-bs-theme="dark"] .flatpickr-day.selected, [data-bs-theme="dark"] .flatpickr-day.startRange, [data-bs-theme="dark"] .flatpickr-day.endRange { color: white !important; }
        .flatpickr-day.inRange { background: rgba(var(--primary-rgb), 0.15); border-color: transparent; box-shadow: -5px 0 0 rgba(var(--primary-rgb), 0.15), 5px 0 0 rgba(var(--primary-rgb), 0.15); }
        .flatpickr-months .flatpickr-month, .flatpickr-current-month .numInputWrapper, .flatpickr-weekday { color: var(--body-color); }
        [data-bs-theme="dark"] .flatpickr-months .flatpickr-month, [data-bs-theme="dark"] .flatpickr-current-month .numInputWrapper, [data-bs-theme="dark"] .flatpickr-weekday { color: var(--body-color); }
        .flatpickr-months .flatpickr-prev-month:hover svg, .flatpickr-months .flatpickr-next-month:hover svg { fill: var(--primary); }
        [data-bs-theme="dark"] .flatpickr-months .flatpickr-prev-month:hover svg, [data-bs-theme="dark"] .flatpickr-months .flatpickr-next-month:hover svg { fill: var(--primary); }
        .flatpickr-day.today { border-color: var(--primary); }
        [data-bs-theme="dark"] .flatpickr-day.today { border-color: var(--primary) !important; color: var(--body-color); }
        .flatpickr-day.today:hover { background-color: var(--table-row-hover-bg); }


        #profit-chart, #cumulative-profit-chart {
            width: 100%;
            max-height: 300px;
            margin-top: 1rem;
            display: none;
            opacity: 0;
        }
        .chart-container {
            position: relative;
            min-height: 320px;
            padding-bottom: 1rem;
        }

        .skeleton {
            background-color: var(--skeleton-bg);
            border-radius: 0.25rem;
            position: relative;
            overflow: hidden;
            display: inline-block;
            vertical-align: middle;
            min-height: 1em;
            transition: background-color var(--transition-speed) var(--transition-ease);
        }
        .skeleton.skeleton-block { display: block; width: 100%; }
        .skeleton::before {
            content: ''; position: absolute; top: 0; left: -150%; width: 150%; height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: var(--skeleton-animation);
        }
        [data-bs-theme="dark"] .skeleton::before {
             background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
        }
        @keyframes skeleton-loading {
            0% { left: -150%; }
            100% { left: 150%; }
        }

        .skeleton-text { width: 80%; height: 1em; margin-bottom: 0.5em; }
        .skeleton-text-short { width: 50%; height: 1em; margin-bottom: 0.5em; }
        .skeleton-h3 { width: 50%; height: 1.75rem; margin-bottom: 0.5rem; border-radius: 0.375rem; }
        .skeleton-badge { width: 80px; height: 24px; border-radius: 6px; }
        .skeleton-status-badge { width: 90px; height: 26px; border-radius: 1rem; }
        .skeleton-btn { width: 60px; height: calc(1.5em + 0.7rem + 2px); border-radius: 0.5rem; }
        .skeleton-chart { width: 100%; height: 300px; border-radius: var(--border-radius); margin-top: 1rem; display: block; }
        .skeleton-input { width: 150px; height: calc(1.5em + 0.9rem + 2px); border-radius: 0.5rem; }


        .stat-card .stat-label .skeleton { width: 70%; height: 0.85rem; margin-bottom: 0.5rem;}
        .stat-card .stat-value .skeleton { width: 50%; height: 1.75rem; margin-bottom: 0.5rem;}
        .stat-card .stat-subtext .skeleton { width: 80%; height: 0.8rem;}
        .stock-performance-card ul span .skeleton { width: 60px; height: 1em; }

        .content-fade-in {
            opacity: 0;
            animation: fadeInAnimation 0.5s ease-in-out forwards;
        }
        @keyframes fadeInAnimation {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 1200px) {
             .stat-card { min-height: 115px; }
             .filter-controls { flex-wrap: wrap; }
        }
        @media (max-width: 992px) {
             .stat-card { min-height: 110px; }
             .table { min-width: 850px; }
             #symbol-search-input { max-width: 160px; }
        }
        @media (max-width: 768px) {
            body { font-size: 0.9rem; }
            .container-xl { padding-left: 0.75rem; padding-right: 0.75rem; }
            .date-range-picker, .deposit-control, .symbol-search-control { flex-direction: column; align-items: stretch; gap: 0.5rem; }
            .date-range-picker input, #deposit-amount-input, #symbol-search-input { max-width: 100%; width: 100%; }
            .date-range-picker label, .deposit-control label, .symbol-search-control label { margin-bottom: 0.25rem; }
            .filter-controls { justify-content: space-between; }
            .table { min-width: 750px; }
            th, td { padding: 0.6rem 0.5rem; font-size: 0.85rem; }
            th:first-child, td:first-child { padding-left: 0.75rem; }
            th:last-child, td:last-child { padding-right: 0.75rem; }
            .stat-card { min-height: 105px; padding: 1rem; }
            .stat-card .stat-value { font-size: 1.4rem; }
            .date-quick-selects .btn { font-size: 0.8rem; padding: 0.3rem 0.6rem;}
        }
        @media (max-width: 576px) {
             .top-nav { flex-direction: column; align-items: flex-start !important; }
             .top-nav > div:last-child { margin-top: 0.5rem; align-self: flex-start; }
             .stat-card { min-height: 95px; }
             .stat-card .stat-value { font-size: 1.25rem; }
             .row.g-3 > * { padding-left: 0.5rem; padding-right: 0.5rem; }
             .filter-controls .btn-group { width: 100%; display: flex; margin-top: 0.5rem; }
             .filter-controls .btn-group .btn { flex-grow: 1; }
             .filter-controls { flex-direction: column; align-items: stretch; }
             .table { min-width: 650px; }
             .deposit-control .d-flex { flex-direction: column; align-items: stretch !important; gap: 0.5rem; }
             #deposit-amount-input { max-width: 100%; }
             .date-quick-selects { flex-wrap: wrap; justify-content: flex-start; gap: 0.25rem !important;}
             .date-quick-selects .btn { flex-grow: 1; min-width: 80px;}
        }
        .swal2-popup {
            background-color: var(--card-bg);
            color: var(--body-color);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius) !important;
            box-shadow: var(--card-shadow-hover);
        }
        .swal2-title { color: var(--body-color); }
        .swal2-html-container { color: var(--body-color); }
        .swal2-close { color: var(--muted); }
        .swal2-close:hover { color: var(--primary); }
        .swal2-input {
            border: 1px solid var(--input-border) !important;
            background-color: var(--input-bg) !important;
            color: var(--body-color) !important;
            border-radius: 0.5rem !important;
            margin-left: 0.5em !important;
            margin-right: 0.5em !important;
            width: calc(100% - 1em) !important;
        }
        .swal2-input:focus {
             border-color: var(--input-focus-border) !important;
             box-shadow: var(--input-focus-shadow) !important;
        }
        .swal2-validation-message {
             background-color: var(--input-bg) !important;
             color: var(--danger) !important;
        }

        .swal2-styled {
             border-radius: 0.5rem !important;
             padding: 0.5rem 1rem !important;
             font-weight: 500 !important;
             transition: all var(--transition-speed) var(--transition-ease) !important;
             box-shadow: none !important;
             margin: 0.3125em;
        }
        .swal2-styled:focus { box-shadow: none !important; }

        .swal2-confirm {
            background-color: var(--primary) !important;
            border: 1px solid var(--primary) !important;
            color: white !important;
        }
        .swal2-confirm:hover {
            background-color: var(--primary-hover) !important;
            border-color: var(--primary-hover) !important;
        }
        .swal2-cancel {
            background-color: transparent !important;
            border: 1px solid var(--input-border) !important;
            color: var(--muted) !important;
        }
        .swal2-cancel:hover {
            background-color: var(--table-row-hover-bg) !important;
            border-color: var(--input-border) !important;
            color: var(--body-color) !important;
        }
        .swal2-loader {
             border-color: var(--primary) transparent var(--primary) transparent !important;
        }

        .swal2-html-container label {
            display: inline-block;
            width: 70px;
            text-align: right;
            margin-right: 10px;
            color: var(--body-color);
        }
         .swal2-html-container .input-pair {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
         }
         .swal2-html-container .input-pair .swal2-input {
             flex-grow: 1;
             margin-left: 0 !important;
             width: auto !important;
         }
         .swal2-toast.swal2-popup {
             box-shadow: var(--card-shadow-hover);
         }
         .swal2-toast .swal2-title {
             color: var(--body-color);
             margin: 0.5em 1em;
         }
         .swal2-toast .swal2-timer-progress-bar {
             background: var(--primary) !important;
         }
    </style>
</head>

<body>
    <div class="container-xl py-4">
        <!-- Header -->
        <div class="d-flex flex-column flex-lg-row justify-content-between align-items-lg-center gap-3 mb-4 top-nav">
            <div>
                <img src="https://shoonya.com/static/img/shoonya_logo_full.a990668.webp" alt="Shoonya Logo"
                    style="height: 30px;">
            </div>
            <div class="d-flex gap-2 align-items-center">
                <button id="refresh-data-btn" class="btn btn-outline-secondary" title="Refresh All Data" aria-label="Refresh data">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home me-1"></i><span>Dashboard</span>
                </a>
                <a href="/papertrade" class="btn btn-primary">
                    <i class="fas fa-file-alt me-1"></i><span>Paper Trades</span>
                </a>
                <button id="theme-toggle" class="btn btn-outline-secondary" aria-label="Toggle theme">
                    <i id="theme-icon" class="fas fa-sun"></i>
                </button>
            </div>
        </div>

        <!-- Stat Cards Row -->
        <div class="row g-3 mb-3">
            <div class="col-6 col-md-4 col-lg">
                <div class="stat-card" id="active-investment-card">
                   <div class="stat-label">Active Investment</div>
                   <div id="active-investment" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                   <div class="stat-subtext">
                       Closed: <span id="consolidated-closed-investment" class="fw-medium">
                           <span class="skeleton skeleton-text-short"></span>
                       </span>
                   </div>
               </div>
           </div>
            <div class="col-6 col-md-4 col-lg">
                 <div class="stat-card" id="unrealized-pnl-card">
                    <div class="stat-label">Unrealized P&L</div>
                    <div id="unrealized-pnl" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                    <div class="stat-subtext">Not booked</div>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg">
                <div class="stat-card" id="realized-pnl-card">
                   <div class="stat-label">Realized P&L</div>
                   <div id="realized-pnl" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                   <div class="stat-subtext">
                       <span id="transaction-charges-display">
                           <span class="skeleton skeleton-text-short"></span>
                       </span>
                       <span class="mx-1 d-inline-block">|</span>
                       Net: <span id="consolidated-net-profit" class="fw-medium">
                           <span class="skeleton skeleton-text-short"></span>
                       </span>
                   </div>
               </div>
           </div>
            <div class="col-6 col-md-4 col-lg">
                <div class="stat-card" id="win-rate-card">
                    <div class="stat-label">Win Rate</div>
                    <div id="win-rate" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                    <div class="stat-subtext">
                        ROI: <span id="consolidated-roi" class="fw-medium">
                            <span class="skeleton skeleton-text-short"></span>
                        </span>
                        <span class="mx-1">|</span>
                        ROD: <span id="consolidated-rod" class="fw-medium">
                            <span class="skeleton skeleton-text-short"></span>
                        </span>
                        <span class="mx-1">|</span>
                        NROD: <span id="net-return-percentage" class="fw-medium">
                            <span class="skeleton skeleton-text-short"></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg">
                <div class="stat-card" id="total-trades-card">
                    <div class="stat-label">Total Trades</div>
                    <div id="total-trades" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                    <div class="stat-subtext">
                        Closed: <span id="consolidated-total-closed" class="fw-medium">
                            <span class="skeleton skeleton-text-short"></span>
                        </span>
                    </div>
                </div>
            </div>
             <div class="col-6 col-md-4 col-lg">
                <div class="stat-card" id="total-open-trades-card">
                    <div class="stat-label">Total Open</div>
                    <div id="total-open-trades" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                    <div class="stat-subtext">Active Trades</div>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg">
                <div class="stat-card emphasized" id="net-available-card">
                   <div class="stat-label">Withdrawable</div>
                   <div id="net-available-funds" class="stat-value"><span class="skeleton skeleton-h3"></span></div>
                   <div class="stat-subtext">Bal. + Inv. - Deposit</div>
               </div>
           </div>
        </div>

        <!-- Filters and Deposit -->
        <div class="card mb-4">
            <div class="card-body d-flex flex-column flex-xl-row justify-content-between align-items-xl-center gap-3 filter-controls">
                <!-- Date Range & Quick Selects -->
                <div class="d-flex flex-column flex-md-row align-items-md-center gap-2 flex-wrap">
                    <label for="date-range" class="text-nowrap fw-medium mb-0 me-2">Date Range:</label>
                    <div class="date-range-picker me-md-2">
                        <input type="text" id="date-range" placeholder="Select date range" data-input>
                    </div>
                    <div class="btn-group btn-group-sm date-quick-selects" role="group" aria-label="Date Quick Selects">
                        <button type="button" class="btn btn-outline-secondary quick-date-btn" data-range="today">Today</button>
                        <button type="button" class="btn btn-outline-secondary quick-date-btn" data-range="7d">7D</button>
                        <button type="button" class="btn btn-outline-secondary quick-date-btn" data-range="30d">30D</button>
                        <button type="button" class="btn btn-outline-secondary quick-date-btn" data-range="mtd">MTD</button>
                        <button type="button" class="btn btn-outline-secondary quick-date-btn" data-range="all">All</button>
                    </div>
                </div>

                <!-- Symbol Search -->
                <div class="symbol-search-control d-flex flex-column flex-md-row align-items-md-center gap-2 flex-wrap">
                    <label for="symbol-search-input" class="text-nowrap fw-medium mb-0">Symbol:</label>
                    <input type="text" id="symbol-search-input" placeholder="Search Symbol...">
                </div>


                 <!-- Deposit Amount -->
                 <div class="deposit-control d-flex flex-column flex-md-row align-items-md-center gap-2 flex-wrap">
                    <label for="deposit-amount-input" class="text-nowrap fw-medium mb-0">Deposited (₹):</label>
                    <div class="d-flex align-items-center gap-2">
                        <span id="deposit-input-loader" class="d-none">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        </span>
                        <span id="deposit-input-container">
                            <input type="number" step="0.01" min="0" id="deposit-amount-input" placeholder="Enter Amount">
                            <span class="skeleton skeleton-input" id="deposit-input-skeleton"></span>
                        </span>
                        <button id="update-deposit-btn" class="btn btn-sm btn-primary">Update</button>
                    </div>
                </div>

                <!-- Trade Status Filter -->
                <div class="d-flex gap-2 align-items-center justify-content-lg-end">
                    <div class="btn-group" role="group" aria-label="Trade Status Filter">
                        <button type="button" class="filter-btn btn btn-sm btn-outline-primary" data-status="all">All</button>
                        <button type="button" class="filter-btn btn btn-sm btn-outline-primary active" data-status="open">Open</button>
                        <button type="button" class="filter-btn btn btn-sm btn-outline-primary" data-status="closed">Closed</button>
                    </div>
                </div>
            </div>
        </div>


        <!-- Trade History Table -->
        <div class="card mb-4">
            <div class="card-body p-0">
                <div class="table-responsive-custom">
                    <table class="table table-hover align-middle mb-0">
                        <thead>
                            <tr>
                                <th data-column="symbol">Stock Symbol</th>
                                <th data-column="buy_price">Buy Price</th>
                                <th data-column="sell_price">Sell Price</th>
                                <th data-column="current_price">Current Price</th>
                                <th data-column="quantity">Quantity</th>
                                <th data-column="invested">Invested</th>
                                <th data-column="returned" title="Returned value for closed trades / Current market value for open trades">Returned / Current Val</th>
                                <th data-column="pnl">P&L</th>
                                <th data-column="pnl_percent" title="Profit/Loss percentage based on invested amount">P&L %</th>
                                <th data-column="duration">Duration</th>
                                <th data-column="status">Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="trade-history-body">
                            <!-- Skeleton Rows - Rendered by JS -->
                            <tr class="skeleton-row">
                                <td><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-status-badge mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-status-badge mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-status-badge mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Performance Summary Section -->
        <div class="stock-performance-card mb-4">
            <h5 class="card-title mb-3">Performance Summary (Closed Trades)</h5>
             <ul class="list-unstyled mb-0">
                <li class="mb-2"><strong>Total Closed Trades:</strong> <span id="summary-total-trades"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Winning Trades:</strong> <span id="summary-winning-trades"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Losing Trades:</strong> <span id="summary-losing-trades"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Avg Profit/Win:</strong> <span id="summary-avg-profit"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Avg Loss/Loss:</strong> <span id="summary-avg-loss"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Largest Winning Trade:</strong> <span id="summary-largest-win"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Largest Losing Trade:</strong> <span id="summary-largest-loss"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Avg P&L % / Trade:</strong> <span id="summary-avg-pnl-percent" title="Average Profit/Loss percentage on invested amount per closed trade"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Avg Holding Duration:</strong> <span id="summary-avg-holding-days" title="Average holding duration in working days for closed trades"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Return on Investment (ROI):</strong> <span id="summary-roi"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Return on Deposit (ROD):</strong> <span id="summary-rod"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Charges (%):</strong> <span id="summary-charges"><span class="skeleton skeleton-text-short"></span></span></li>
                <li class="mb-2"><strong>Net Return on Deposit:</strong> <span id="summary-new-rod"><span class="skeleton skeleton-text-short"></span></span></li>
                <li><strong>Profit Factor:</strong> <span id="summary-profit-factor"><span class="skeleton skeleton-text-short"></span></span></li>
            </ul>
        </div>

        <!-- Charts -->
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body chart-container">
                        <h5 class="card-title">Daily Profit & Loss</h5>
                        <div id="profit-chart-loader" class="skeleton skeleton-chart skeleton-block"></div>
                        <canvas id="profit-chart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body chart-container">
                        <h5 class="card-title">Cumulative Net Profit</h5>
                         <div id="cumulative-profit-chart-loader" class="skeleton skeleton-chart skeleton-block"></div>
                        <canvas id="cumulative-profit-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- /container -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.5/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <script>
        // Helper function to count working days (Mon-Fri)
        // d1 is inclusive, d2 is exclusive
        function countWorkingDays(d1, d2) {
            const startDate = (d1 instanceof Date) ? new Date(d1.getTime()) : new Date(d1);
            const endDate = (d2 instanceof Date) ? new Date(d2.getTime()) : new Date(d2);

            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                return 0;
            }

            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(0, 0, 0, 0);

            if (startDate >= endDate) {
                return 0;
            }

            let currentDate = new Date(startDate);
            let workingDays = 0;

            while (currentDate < endDate) {
                const dayOfWeek = currentDate.getDay(); // 0 (Sun) to 6 (Sat)
                if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Monday to Friday
                    workingDays++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
            return workingDays;
        }

        document.addEventListener('DOMContentLoaded', function() {
            // --- Theme Toggle ---
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = document.getElementById('theme-icon');
            let isDarkMode = localStorage.getItem('theme') === 'dark' ||
                             (localStorage.getItem('theme') === null && window.matchMedia('(prefers-color-scheme: dark)').matches);

            const updateTheme = (dark) => {
                isDarkMode = dark;
                const newTheme = isDarkMode ? 'dark' : 'light';
                document.documentElement.setAttribute('data-bs-theme', newTheme);
                themeIcon.className = `fas fa-${isDarkMode ? 'moon' : 'sun'}`;
                const span = themeToggle.querySelector('span.d-none.d-lg-inline');
                if(span) span.textContent = isDarkMode ? 'Dark' : 'Light';
                localStorage.setItem('theme', newTheme);
                updateChartColors(window.myChart);
                updateChartColors(window.myCumulativeChart);
                Swal.update({
                    customClass: { popup: `swal2-${newTheme}` }
                 });
                 if (window.datePicker && window.datePicker.calendarContainer) {
                     window.datePicker.calendarContainer.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--card-bg').trim();
                     window.datePicker.calendarContainer.style.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--card-border').trim();
                     window.datePicker.redraw();
                 }
            };

            themeToggle.addEventListener('click', () => updateTheme(!isDarkMode));
            updateTheme(isDarkMode);
            window.datePicker = flatpickr("#date-range", {
                mode: "range",
                dateFormat: "Y-m-d",
                onChange: function(selectedDates, dateStr, instance) {
                    if ((selectedDates.length === 2) || selectedDates.length === 0) {
                        applyFilters();
                    }
                },
            });

            // --- Quick Date Selectors ---
            document.querySelectorAll('.quick-date-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const range = this.dataset.range;
                    const today = new Date();
                    let startDate, endDate;

                    switch(range) {
                        case 'today':
                            startDate = today;
                            endDate = today;
                            break;
                        case '7d':
                            endDate = today;
                            startDate = new Date();
                            startDate.setDate(today.getDate() - 6);
                            break;
                        case '30d':
                            endDate = today;
                            startDate = new Date();
                            startDate.setDate(today.getDate() - 29);
                            break;
                        case 'mtd':
                            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                            endDate = today;
                            break;
                        case 'all':
                            window.datePicker.clear();
                            return;
                        default:
                            return;
                    }
                    window.datePicker.setDate([startDate, endDate], true);
                });
            });

            // --- Symbol Search ---
            const symbolSearchInput = document.getElementById('symbol-search-input');
            symbolSearchInput.addEventListener('input', applyFilters);


            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    applyFilters();
                });
            });

            let currentSort = { column: 'buy_date', ascending: false };
            document.querySelectorAll('th[data-column]').forEach(th => {
                th.addEventListener('click', function() {
                    const column = this.dataset.column;
                    const isAscending = currentSort.column === column ? !currentSort.ascending : true;
                    currentSort = { column, ascending: isAscending };
                    applyFilters();
                });
            });

            // --- Deposit Amount ---
            const depositInput = document.getElementById('deposit-amount-input');
            const updateDepositBtn = document.getElementById('update-deposit-btn');
            const depositInputContainer = document.getElementById('deposit-input-container');
            const depositInputSkeleton = document.getElementById('deposit-input-skeleton');
            const depositInputLoader = document.getElementById('deposit-input-loader');

            function showDepositInputSkeleton(show = true) {
                if (!depositInputContainer || !depositInputSkeleton) return;
                depositInputContainer.style.display = show ? 'none' : 'inline-block';
                depositInputSkeleton.style.display = show ? 'inline-block' : 'none';
            }
            function showDepositLoader(show = true) {
                if(!depositInputLoader || !updateDepositBtn) return;
                depositInputLoader.classList.toggle('d-none', !show);
                updateDepositBtn.disabled = show;
            }

            async function loadDepositAmount() {
                showDepositInputSkeleton(true);
                try {
                    const response = await fetch('/api/deposit_amount');
                    if (!response.ok) throw new Error('Failed to fetch deposit amount');
                    const data = await response.json();
                    window.currentDepositAmount = data.amount;
                    depositInput.value = data.amount.toFixed(2);
                    showDepositInputSkeleton(false);
                    calculateAndUpdateNetAvailable();
                } catch (error) {
                    console.error('Error loading deposit amount:', error);
                    depositInput.value = 'Error';
                    depositInput.disabled = true;
                    updateDepositBtn.disabled = true;
                    showDepositInputSkeleton(false);
                    calculateAndUpdateNetAvailable();
                }
            }

            updateDepositBtn.addEventListener('click', async () => {
                const newAmountStr = depositInput.value;
                let newAmount;
                try {
                    newAmount = parseFloat(newAmountStr);
                    if (isNaN(newAmount) || newAmount < 0) {
                        throw new Error('Invalid deposit amount. Must be a non-negative number.');
                    }
                } catch (error) {
                     showToast(error.message, 'error');
                     return;
                }

                showDepositLoader(true);
                try {
                    const response = await fetch('/api/deposit_amount', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ amount: newAmount })
                    });
                    const data = await response.json().then(d => ({ ok: response.ok, data: d }));

                    if (!data.ok || data.data.status !== 'success') {
                        throw new Error(data.data.error || 'Failed to update deposit amount.');
                    }

                    window.currentDepositAmount = data.data.amount;
                    depositInput.value = data.data.amount.toFixed(2);
                    showToast('Deposit amount updated successfully!', 'success');
                    calculateAndUpdateNetAvailable();
                } catch (error) {
                    console.error('Error updating deposit amount:', error);
                    showToast(error.message, 'error');
                } finally {
                    showDepositLoader(false);
                }
            });

            window.tradeData = [];
            window.currentDepositAmount = null;
            window.currentAvailableBalance = null;
            window.currentActiveInvestment = null;

            const refreshDataBtn = document.getElementById('refresh-data-btn');
            refreshDataBtn.addEventListener('click', loadData);


            async function loadData() {
                try {
                    renderTableSkeletons('trade-history-body', 12, 3);
                    const [tradeHistoryResponse, limitsResponse, depositResponse] = await Promise.allSettled([
                        fetch('/api/trade_history'),
                        fetch('/api/limits'),
                        fetch('/api/deposit_amount')
                    ]);

                    if (tradeHistoryResponse.status === 'fulfilled' && tradeHistoryResponse.value.ok) {
                         const data = await tradeHistoryResponse.value.json();
                         window.tradeData = data;
                         updateMetrics(window.tradeData);
                         applyFilters();
                    } else {
                        throw new Error(`Failed to load trade history: ${tradeHistoryResponse.reason || tradeHistoryResponse.value.statusText}`);
                    }

                    if (limitsResponse.status === 'fulfilled' && limitsResponse.value.ok) {
                         const limitsData = await limitsResponse.value.json();
                         window.currentAvailableBalance = limitsData.cash;
                    } else {
                        console.warn('Failed to load limits (available balance):', limitsResponse.reason || limitsResponse.value.statusText);
                         window.currentAvailableBalance = null;
                    }

                     if (depositResponse.status === 'fulfilled' && depositResponse.value.ok) {
                         const depositData = await depositResponse.value.json();
                         window.currentDepositAmount = depositData.amount;
                         depositInput.value = depositData.amount.toFixed(2);
                         showDepositInputSkeleton(false);
                     } else {
                         console.error('Failed to load deposit amount:', depositResponse.reason || depositResponse.value.statusText);
                         depositInput.value = 'Error';
                         depositInput.disabled = true;
                         updateDepositBtn.disabled = true;
                         window.currentDepositAmount = null;
                         showDepositInputSkeleton(false);
                     }

                    calculateAndUpdateNetAvailable();
                    updateCurrentPricesForVisibleRows();

                } catch (error) {
                    console.error('Error loading data:', error);
                    showLoadingError(error.message || 'Failed to load page data.');
                     showDepositInputSkeleton(false);
                     depositInput.value = 'Error';
                     depositInput.disabled = true;
                     updateDepositBtn.disabled = true;
                     calculateAndUpdateNetAvailable();
                }
            }

             function calculateAndUpdateNetAvailable() {
                const deposit = window.currentDepositAmount;
                const balance = window.currentAvailableBalance;
                const invested = window.currentActiveInvestment;

                 let netAvailable = null;
                 let statusText = "Bal. + Inv. - Deposit";
                 let displayValue = '<span class="skeleton skeleton-h3"></span>';

                 if (deposit !== null && balance !== null && invested !== null) {
                     netAvailable = balance + invested - deposit;
                     displayValue = `₹${netAvailable.toFixed(2)}`;
                 } else {
                     const missing = [];
                     if (deposit === null) missing.push('Deposit');
                     if (balance === null) missing.push('Balance');
                     if (invested === null) missing.push('Investment');

                     if (missing.length > 0) {
                         statusText = `Error loading: ${missing.join(', ')}`;
                         displayValue = '<span class="text-danger">Error</span>';
                     } else {
                         statusText = "Calculating...";
                     }
                 }

                 const cardElement = document.getElementById('net-available-funds');
                 const subtextElement = document.querySelector('#net-available-card .stat-subtext');

                 if (cardElement) {
                     if (displayValue !== '<span class="skeleton skeleton-h3"></span>') {
                         cardElement.innerHTML = displayValue;
                         if (!cardElement.classList.contains('content-fade-in')) {
                              setTimeout(() => cardElement.classList.add('content-fade-in'), 10);
                         }
                          cardElement.classList.remove('profit-positive', 'profit-negative');
                          if (netAvailable !== null) {
                              cardElement.classList.add(netAvailable >= 0 ? 'profit-positive' : 'profit-negative');
                          }

                     } else {
                         cardElement.innerHTML = displayValue;
                     }
                 }
                  if (subtextElement) {
                      const skeleton = subtextElement.querySelector('.skeleton');
                      if (skeleton) skeleton.remove();
                      subtextElement.textContent = statusText;
                  }
             }


            function updateMetrics(data) {
                const closedTrades = data.filter(t => t.sell_price && t.sell_date);
                const activeTrades = data.filter(t => !(t.sell_price && t.sell_date));

                const totalTrades = data.length;
                const totalClosedTrades = closedTrades.length;
                const totalOpenTrades = activeTrades.length;
                const activeInvestment = activeTrades.reduce((acc, t) => acc + (t.total_quantity * t.buy_price_avg), 0);
                window.currentActiveInvestment = activeInvestment;

                const closedInvestment = closedTrades.reduce((acc, t) => acc + (t.total_quantity * t.buy_price_avg), 0);
                const realizedPNL = closedTrades.reduce((acc, t) => acc + ((t.sell_price - t.buy_price_avg) * t.total_quantity), 0);
                const transactionCharges = closedTrades.length * 12;
                const netProfit = realizedPNL - transactionCharges;
                const winningTradesCount = closedTrades.filter(t => (t.sell_price - t.buy_price_avg) > 0).length;
                const winRate = closedTrades.length > 0 ? (winningTradesCount / closedTrades.length) * 100 : 0;
                const roi = closedInvestment !== 0 ? (netProfit / closedInvestment) * 100 : 0;

                const losingTradesCount = closedTrades.length - winningTradesCount;
                let largestWin = 0;
                let largestLoss = 0;
                let totalPnlPercent = 0;
                let validPnlPercentCount = 0;

                const totalProfitOnWins = closedTrades
                    .filter(t => (t.sell_price - t.buy_price_avg) > 0)
                    .reduce((acc, t) => {
                        const pnl = ((t.sell_price - t.buy_price_avg) * t.total_quantity);
                        if (pnl > largestWin) largestWin = pnl;
                        if (t.buy_price_avg > 0) {
                            const investedVal = t.total_quantity * t.buy_price_avg;
                            totalPnlPercent += (pnl / investedVal) * 100;
                            validPnlPercentCount++;
                        }
                        return acc + pnl;
                    }, 0);

                const totalLossOnLosses = closedTrades
                    .filter(t => (t.sell_price - t.buy_price_avg) < 0)
                    .reduce((acc, t) => {
                        const pnl = ((t.sell_price - t.buy_price_avg) * t.total_quantity);
                        if (pnl < largestLoss) largestLoss = pnl;
                        if (t.buy_price_avg > 0) {
                             const investedVal = t.total_quantity * t.buy_price_avg;
                             totalPnlPercent += (pnl / investedVal) * 100;
                             validPnlPercentCount++;
                         }
                        return acc + pnl;
                    }, 0);

                const avgProfitPerWin = winningTradesCount > 0 ? totalProfitOnWins / winningTradesCount : 0;
                const avgLossPerLoss = losingTradesCount > 0 ? totalLossOnLosses / losingTradesCount : 0;
                const avgPnlPercent = validPnlPercentCount > 0 ? totalPnlPercent / validPnlPercentCount : 0;

                let totalHoldingWorkingDays = 0;
                let closedTradesWithValidDuration = 0;
                if (closedTrades.length > 0) {
                    closedTrades.forEach(t => {
                         if (t.buy_date && t.sell_date) {
                            try {
                                const buyDate = new Date(t.buy_date);
                                const sellDate = new Date(t.sell_date);
                                if (!isNaN(buyDate.getTime()) && !isNaN(sellDate.getTime()) && sellDate > buyDate) {
                                     totalHoldingWorkingDays += countWorkingDays(buyDate, sellDate);
                                     closedTradesWithValidDuration++;
                                }
                             } catch (e) { console.warn("Date parsing error for avg duration:", t, e); }
                         }
                    });
                }
                const avgHoldingWorkingDays = closedTradesWithValidDuration > 0 ? totalHoldingWorkingDays / closedTradesWithValidDuration : 0;


                 const absTotalLoss = Math.abs(totalLossOnLosses);
                 let profitFactor = 'N/A';
                 if (absTotalLoss > 0) {
                     profitFactor = (totalProfitOnWins / absTotalLoss).toFixed(2);
                 } else if (totalProfitOnWins > 0 && absTotalLoss === 0) {
                     profitFactor = '∞';
                 }

                 let totalUnrealizedPNL = 0;
                 activeTrades.forEach(t => {
                      if (t.current_price !== null && typeof t.current_price !== 'undefined' && !isNaN(t.current_price)) {
                          totalUnrealizedPNL += (t.current_price - t.buy_price_avg) * t.total_quantity;
                      }
                 });

                updateElement('active-investment', `₹${activeInvestment.toFixed(2)}`);
                updateElement('unrealized-pnl', `₹${totalUnrealizedPNL.toFixed(2)}`, totalUnrealizedPNL);
                updateElement('realized-pnl', `₹${realizedPNL.toFixed(2)}`, realizedPNL);
                updateElement('win-rate', `${winRate.toFixed(1)}%`, winRate >= 50 ? 1 : -1);
                updateElement('total-trades', totalTrades);
                updateElement('total-open-trades', totalOpenTrades);

                updateElement('transaction-charges-display', `Charge:~₹${transactionCharges.toFixed(2)}`);
                updateElement('consolidated-net-profit', `₹${netProfit.toFixed(2)}`, netProfit);
                updateElement('consolidated-closed-investment', `₹${closedInvestment.toFixed(2)}`);
                updateElement('consolidated-roi', `${roi.toFixed(1)}%`, roi);

                const depositAmount = parseFloat(depositInput.value) || 0; // Ensure depositAmount is a number, default to 0 if NaN
                let currentBalance = depositAmount + realizedPNL + totalUnrealizedPNL;

                let netReturnPercentage = 0;
                if (depositAmount && depositAmount > 0) {
                    netReturnPercentage = (netProfit / depositAmount) * 100;
                }
                updateElement('net-return-percentage', `${netReturnPercentage.toFixed(1)}%`, netReturnPercentage);

                let rod = 0;
                if (depositAmount && depositAmount > 0 && realizedPNL) {
                    rod = (realizedPNL / depositAmount) * 100;
                }
                updateElement('consolidated-rod', `${rod.toFixed(1)}%`, rod);

                updateElement('consolidated-total-closed', totalClosedTrades);

                updateElement('summary-total-trades', closedTrades.length);
                updateElement('summary-winning-trades', winningTradesCount);
                updateElement('summary-losing-trades', losingTradesCount);
                updateElement('summary-avg-profit', `₹${avgProfitPerWin.toFixed(2)}`, avgProfitPerWin);
                updateElement('summary-avg-loss', `₹${avgLossPerLoss.toFixed(2)}`, avgLossPerLoss);
                updateElement('summary-largest-win', `₹${largestWin.toFixed(2)}`, largestWin);
                updateElement('summary-largest-loss', `₹${largestLoss.toFixed(2)}`, largestLoss);
                updateElement('summary-avg-pnl-percent', `${avgPnlPercent.toFixed(2)}%`, avgPnlPercent);
                
                updateElement('summary-avg-holding-days', `${avgHoldingWorkingDays.toFixed(1)} d`);
                const summaryAvgHoldingDaysSpan = document.getElementById('summary-avg-holding-days');
                if (summaryAvgHoldingDaysSpan) {
                    summaryAvgHoldingDaysSpan.title = "Average holding duration in working days for closed trades";
                }

                updateElement('summary-roi', `${roi.toFixed(1)}%`, roi);
                updateElement('summary-rod', `${rod.toFixed(1)}%`, rod);
                let chargesPercent = 0;
                if (depositAmount && depositAmount > 0) {
                    chargesPercent = (transactionCharges / depositAmount) * 100;
                }
                updateElement('summary-charges', `${chargesPercent.toFixed(2)}%`, chargesPercent);
                updateElement('summary-new-rod', `${netReturnPercentage.toFixed(1)}%`, netReturnPercentage);

                updateElement('summary-profit-factor', profitFactor);

                 calculateAndUpdateNetAvailable();
            }

            function updateElement(id, value, pnlValue = null) {
                 const element = document.getElementById(id);
                 const parentCard = element ? element.closest('.stat-card, .stock-performance-card') : null;

                 if (element) {
                     if(id !== 'net-available-funds') {
                        element.querySelectorAll('.skeleton').forEach(sk => sk.remove());
                     }

                    if(id !== 'net-available-funds') {
                         element.textContent = value;
                    }

                    if(!element.classList.contains('content-fade-in')) {
                         setTimeout(() => element.classList.add('content-fade-in'), 10);
                    }

                    if(id !== 'net-available-funds') {
                        element.classList.remove('profit-positive', 'profit-negative');
                         if (pnlValue !== null && typeof pnlValue === 'number') {
                             element.classList.add(pnlValue >= 0 ? 'profit-positive' : 'profit-negative');
                         }
                    }
                    if (element.closest('.stat-subtext')) {
                         element.classList.remove('profit-positive', 'profit-negative');
                         if (pnlValue !== null && typeof pnlValue === 'number') {
                            element.classList.add(pnlValue >= 0 ? 'profit-positive' : 'profit-negative');
                         }
                    }
                 }

                  if (parentCard && id !== 'net-available-funds') {
                    setTimeout(() => {
                         parentCard.querySelectorAll('.skeleton').forEach(sk => sk.remove());
                         if (!parentCard.classList.contains('content-fade-in')) {
                            parentCard.classList.add('content-fade-in');
                         }
                    }, 10);
                  }
            }


            async function updateCurrentPricesForVisibleRows() {
                const tbody = document.getElementById('trade-history-body');
                const rows = tbody.querySelectorAll('tr[data-id]');
                const promises = [];

                rows.forEach(row => {
                    const tradeId = row.dataset.id;
                    let symbol = row.dataset.symbol;
                    const status = row.dataset.status;
                    const currentPriceCell = row.querySelector('.current-price');
                    const currentPriceCellContent = currentPriceCell?.innerHTML || '';


                    if (!symbol) {
                        const trade = window.tradeData.find(t => (t.id || `${t.symbol}-${t.buy_date}`) === tradeId);
                        if (trade) symbol = trade.symbol;
                    }

                    const tradeIndex = window.tradeData.findIndex(t => (t.id || `${t.symbol}-${t.buy_date}`) === tradeId);
                    const trade = (tradeIndex > -1) ? window.tradeData[tradeIndex] : null;

                    if (symbol && (currentPriceCellContent.includes('skeleton') || status === 'open')) {
                         promises.push(
                            fetch(`/api/current_price?symbol=${symbol}`)
                                .then(response => response.ok ? response.json() : Promise.reject('Failed fetch'))
                                .then(priceData => {
                                    if (tradeIndex > -1) {
                                        trade.current_price = priceData.current_price;
                                        updateTableRowCurrentPriceOnly(row, trade.current_price);
                                        if (status === 'open') {
                                            updateTableRowOpenTradeDerivedFields(row, trade);
                                        }
                                    }
                                })
                                .catch(error => {
                                    console.warn(`Failed to fetch current price for ${symbol}:`, error);
                                    if (tradeIndex > -1) {
                                        trade.current_price = null;
                                    }
                                    if (currentPriceCell && currentPriceCellContent.includes('skeleton')) {
                                        updateTableRowCurrentPriceOnly(row, null, true);
                                    } else if (currentPriceCell && status === 'open') {
                                        updateTableRowCurrentPriceOnly(row, null);
                                        updateTableRowOpenTradeDerivedFields(row, trade);
                                    }
                                })
                         );
                    } else if (status === 'closed' && currentPriceCellContent.includes('skeleton')) {
                         updateTableRowCurrentPriceOnly(row, null);
                    }
                });
                await Promise.all(promises);
                 updateMetrics(window.tradeData);
            }

            function updateTableRowCurrentPriceOnly(row, currentPrice, isError = false) {
                if (!row) return;
                const currentPriceCell = row.querySelector('.current-price');
                if (currentPriceCell) {
                    const skeleton = currentPriceCell.querySelector('.skeleton');
                    if (skeleton) skeleton.remove();

                    let displayValue = '–';
                    if (isError) {
                        displayValue = '<span class="text-danger">Error</span>';
                    } else if (currentPrice !== null && typeof currentPrice !== 'undefined') {
                         displayValue = `₹${currentPrice.toFixed(2)}`;
                    }
                    currentPriceCell.innerHTML = displayValue;
                }
            }


            function updateTableRowOpenTradeDerivedFields(row, trade) {
                if (!row || !trade || (trade.sell_price && trade.sell_date)) return;

                const returnedCell = row.querySelector('.returned');
                const pnlCell = row.querySelector('.pnl');
                const pnlPercentCell = row.querySelector('.pnl-percent');

                const invested = trade.total_quantity * trade.buy_price_avg;
                const currentPrice = trade.current_price;
                const currentValue = (currentPrice !== null && typeof currentPrice !== 'undefined')
                                      ? currentPrice * trade.total_quantity
                                      : null;
                const pnl = currentValue !== null ? currentValue - invested : null;
                let pnlPercent = null;
                 if (pnl !== null && invested > 0) {
                    pnlPercent = (pnl / invested) * 100;
                 }

                if (returnedCell) {
                    const skeleton = returnedCell.querySelector('.skeleton');
                    if (skeleton) skeleton.remove();
                    returnedCell.textContent = currentValue !== null ? `₹${currentValue.toFixed(2)}` : '–';
                }
                if (pnlCell) {
                    const skeleton = pnlCell.querySelector('.skeleton');
                    if (skeleton) skeleton.remove();
                    pnlCell.textContent = pnl !== null ? `${pnl >= 0 ? '+' : ''}₹${pnl.toFixed(2)}` : '–';
                    pnlCell.classList.remove('profit-positive', 'profit-negative');
                    if (pnl !== null) {
                        pnlCell.classList.add(pnl >= 0 ? 'profit-positive' : 'profit-negative');
                    }
                }
                if (pnlPercentCell) {
                    const skeleton = pnlPercentCell.querySelector('.skeleton');
                    if (skeleton) skeleton.remove();
                    pnlPercentCell.textContent = pnlPercent !== null ? `${pnlPercent.toFixed(2)}%` : '–';
                    pnlPercentCell.classList.remove('profit-positive', 'profit-negative');
                    if (pnlPercent !== null) {
                         pnlPercentCell.classList.add(pnlPercent >= 0 ? 'profit-positive' : 'profit-negative');
                    }
                }
            }


            function renderTable(data) {
                const tbody = document.getElementById('trade-history-body');
                const numCols = 12;
                if (!data || data.length === 0) {
                     tbody.innerHTML = `<tr><td colspan="${numCols}" class="text-center py-4 text-muted">No trades found matching your criteria.</td></tr>`;
                     return;
                }

                 tbody.innerHTML = '';

                let rowHTML = '';
                data.forEach((trade, index) => {
                    const invested = trade.total_quantity * trade.buy_price_avg;
                    const isClosed = !!trade.sell_price && !!trade.sell_date;
                    const sellPrice = isClosed ? trade.sell_price : null;
                    const currentPrice = trade.current_price;

                    let returnedValue = null;
                    let pnl = null;
                    let pnlPercent = null;
                    let returnedDisplay = '<span class="skeleton skeleton-text-short mx-auto skeleton-block"></span>';
                    let pnlDisplay = '<span class="skeleton skeleton-text-short mx-auto skeleton-block"></span>';
                    let pnlPercentDisplay = '<span class="skeleton skeleton-text-short mx-auto skeleton-block"></span>';

                    if (isClosed) {
                        returnedValue = trade.total_quantity * sellPrice;
                        pnl = returnedValue - invested;
                        returnedDisplay = `₹${returnedValue.toFixed(2)}`;
                        pnlDisplay = `${pnl >= 0 ? '+' : ''}₹${pnl.toFixed(2)}`;
                        if (invested > 0) {
                           pnlPercent = (pnl / invested) * 100;
                           pnlPercentDisplay = `${pnlPercent.toFixed(2)}%`;
                        } else {
                           pnlPercentDisplay = 'N/A';
                        }

                    } else {
                        if (currentPrice !== null && typeof currentPrice !== 'undefined') {
                            returnedValue = trade.total_quantity * currentPrice;
                            pnl = returnedValue - invested;
                            returnedDisplay = `₹${returnedValue.toFixed(2)}`;
                            pnlDisplay = `${pnl >= 0 ? '+' : ''}₹${pnl.toFixed(2)}`;
                            if (invested > 0) {
                                pnlPercent = (pnl / invested) * 100;
                                pnlPercentDisplay = `${pnlPercent.toFixed(2)}%`;
                            } else {
                                pnlPercentDisplay = 'N/A';
                            }
                        } else if (typeof currentPrice === 'undefined') {
                        } else {
                           returnedDisplay = '–';
                           pnlDisplay = '–';
                           pnlPercentDisplay = '–';
                        }
                    }

                    let buyDateObj; // Renamed from buyDate to avoid conflict in this scope
                    let sellDateObj;
                    let durationText = 'N/A';
                    let durationTooltip = '';

                    try {
                        buyDateObj = new Date(trade.buy_date);
                        if (isNaN(buyDateObj.getTime())) throw new Error("Invalid buy date");
                        const buyDateFormatted = buyDateObj.toLocaleDateString('en-CA') + ' ' + buyDateObj.toLocaleTimeString();
                        durationTooltip = `Bought: ${buyDateFormatted}`;


                        if (isClosed && trade.sell_date) {
                            sellDateObj = new Date(trade.sell_date);
                             if (isNaN(sellDateObj.getTime())) throw new Error("Invalid sell date");
                             const sellDateFormatted = sellDateObj.toLocaleDateString('en-CA') + ' ' + sellDateObj.toLocaleTimeString();
                             durationTooltip = `Bought: ${buyDateFormatted}\nSold: ${sellDateFormatted}`;

                            const durationMs = sellDateObj - buyDateObj;
                            const fullDayMs = 1000 * 60 * 60 * 24;

                            if (durationMs < fullDayMs &&
                                buyDateObj.getFullYear() === sellDateObj.getFullYear() &&
                                buyDateObj.getMonth() === sellDateObj.getMonth() &&
                                buyDateObj.getDate() === sellDateObj.getDate()) { // Same calendar day
                                if (durationMs < 1000 * 60) durationText = '< 1m';
                                else if (durationMs < 1000 * 60 * 60) durationText = `${Math.floor(durationMs / (1000 * 60))}m`;
                                else durationText = `${Math.floor(durationMs / (1000 * 60 * 60))}h`;
                            } else { // Spans across midnight or is >= 24 hours
                                const workingDaysHeld = countWorkingDays(buyDateObj, sellDateObj);
                                durationText = `${workingDaysHeld} d`;
                                durationTooltip += `\nDuration: ${workingDaysHeld} working day(s)`;
                            }
                        } else if (!isClosed) {
                             const today = new Date();
                             const durationMs = today - buyDateObj;
                             
                             let tomorrowForOpen = new Date(today);
                             tomorrowForOpen.setDate(today.getDate() + 1); // To include today in countWorkingDays
                             const workingDaysOpen = countWorkingDays(buyDateObj, tomorrowForOpen);

                             if (buyDateObj.getFullYear() === today.getFullYear() &&
                                 buyDateObj.getMonth() === today.getMonth() &&
                                 buyDateObj.getDate() === today.getDate()) { // Bought today
                                 if (durationMs < 1000 * 60) durationText = '< 1m open';
                                 else if (durationMs < 1000 * 60 * 60) durationText = `${Math.floor(durationMs / (1000 * 60))}m open`;
                                 else durationText = `${Math.floor(durationMs / (1000 * 60 * 60))}h open`;
                                 durationTooltip += `\nOpen for < 1 working day`;
                             } else { // Bought on a previous day
                                 durationText = `Open ${workingDaysOpen} d`;
                                 durationTooltip += `\nOpen for ${workingDaysOpen} working day(s)`;
                             }
                        }
                    } catch (e) {
                        console.warn("Error calculating duration:", e, trade);
                        durationText = 'Error';
                    }


                    const tradeId = trade.id || `${trade.symbol}-${trade.buy_date}`;
                    let currentPriceDisplay;
                    if (typeof currentPrice === 'undefined') {
                        currentPriceDisplay = '<span class="skeleton skeleton-text-short mx-auto skeleton-block"></span>';
                    } else if (currentPrice !== null) {
                        currentPriceDisplay = `₹${currentPrice.toFixed(2)}`;
                    } else {
                        currentPriceDisplay = '–';
                    }

                    const rowClass = isClosed ? 'trade-row-closed' : 'trade-row-open';

                    rowHTML += `
                    <tr data-status="${isClosed ? 'closed' : 'open'}" data-buy-date="${trade.buy_date || ''}" data-id="${tradeId}" data-symbol="${trade.symbol}" class="content-fade-in ${rowClass}" style="animation-delay: ${index * 0.03}s;">
                        <td>
                            <a href="https://www.tradingview.com/chart/?symbol=NSE%3A${trade.symbol.split('-')[0]}" target="_blank" class="stock-badge">
                                ${trade.symbol}
                            </a>
                        </td>
                        <td>₹${trade.buy_price_avg.toFixed(2)}</td>
                        <td>${sellPrice ? `₹${sellPrice.toFixed(2)}` : '–'}</td>
                        <td class="current-price">${currentPriceDisplay}</td>
                        <td>${trade.total_quantity}</td>
                        <td>₹${invested.toFixed(2)}</td>
                        <td class="returned" title="${isClosed ? 'Value at time of selling' : 'Current market value of open position'}">${returnedDisplay}</td>
                        <td class="pnl ${pnl !== null ? (pnl >= 0 ? 'profit-positive' : 'profit-negative') : ''}">
                            ${pnlDisplay}
                        </td>
                         <td class="pnl-percent ${pnlPercent !== null ? (pnlPercent >= 0 ? 'profit-positive' : 'profit-negative') : ''}">
                            ${pnlPercentDisplay}
                        </td>
                        <td title="${durationTooltip}">${durationText}</td>
                        <td>
                            <span class="status-badge ${isClosed ? 'bg-success-soft' : 'bg-warning-soft'}">
                                <i class="mdi ${isClosed ? 'mdi-check-circle-outline' : 'mdi-progress-clock'}"></i>
                                ${isClosed ? 'Closed' : 'Open'}
                            </span>
                        </td>
                         <td class="actions-cell">
                             ${!isClosed ? `
                             <button class="btn btn-sm btn-outline-secondary edit-trade-btn"
                                     data-symbol="${trade.symbol}"
                                     data-buy-date="${trade.buy_date}"
                                     title="Edit Buy Price/Quantity"
                                     aria-label="Edit trade">
                                 <i class="mdi mdi-square-edit-outline"></i>
                             </button>` : ''}
                         </td>
                    </tr>`;
                });

                tbody.innerHTML = rowHTML;

                tbody.querySelectorAll('.edit-trade-btn').forEach(button => {
                    button.addEventListener('click', handleEditTradeClick);
                });
                 updateCurrentPricesForVisibleRows();
            }

             function applyFilters() {
                let filteredData = [...window.tradeData];

                const activeStatus = document.querySelector('.filter-btn.active')?.dataset.status || 'all';
                if (activeStatus !== 'all') {
                    filteredData = filteredData.filter(row => (activeStatus === 'open' ? !(row.sell_price && row.sell_date) : (!!row.sell_price && !!row.sell_date)));
                }

                const selectedDates = datePicker.selectedDates;
                if (selectedDates.length === 2) {
                    const startDate = selectedDates[0].setHours(0,0,0,0);
                    const endDate = selectedDates[1].setHours(23,59,59,999);
                    filteredData = filteredData.filter(row => {
                         if (!row.buy_date) return false;
                         try {
                             const buyDate = new Date(row.buy_date).getTime();
                             return !isNaN(buyDate) && buyDate >= startDate && buyDate <= endDate;
                         } catch (e) {
                             console.warn("Date filter error:", e, row);
                             return false;
                         }
                    });
                }

                const searchTerm = symbolSearchInput.value.trim().toLowerCase();
                if (searchTerm) {
                    filteredData = filteredData.filter(trade =>
                        trade.symbol.toLowerCase().includes(searchTerm) ||
                        (trade.name && trade.name.toLowerCase().includes(searchTerm))
                    );
                }

                const { column, ascending } = currentSort;
                filteredData.sort((a, b) => {
                    let valueA = getSortValue(a, column);
                    let valueB = getSortValue(b, column);
                     const aIsNull = valueA === null || typeof valueA === 'undefined';
                     const bIsNull = valueB === null || typeof valueB === 'undefined';

                     if (aIsNull && bIsNull) return 0;
                     if (aIsNull) return ascending ? -1 : 1;
                     if (bIsNull) return ascending ? 1 : -1;

                    if (typeof valueA === 'string') {
                        return ascending ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
                    } else {
                        return ascending ? valueA - valueB : valueB - valueA;
                    }
                });

                 document.querySelectorAll('th[data-column]').forEach(th => {
                    th.classList.remove('asc', 'desc');
                    if (th.dataset.column === column) {
                        th.classList.add(ascending ? 'asc' : 'desc');
                    }
                 });

                renderTable(filteredData);

                 let chartData = [...window.tradeData];
                 if (selectedDates.length === 2) {
                     const startDate = selectedDates[0].setHours(0,0,0,0);
                     const endDate = selectedDates[1].setHours(23,59,59,999);
                     chartData = chartData.filter(row => {
                         if (!row.sell_date) return false;
                         try {
                            const tradeDate = new Date(row.sell_date).getTime();
                            return !isNaN(tradeDate) && tradeDate >= startDate && tradeDate <= endDate;
                         } catch (e) { return false; }
                     });
                 } else {
                     chartData = chartData.filter(row => row.sell_date && row.sell_price);
                 }
                 updateChart(chartData);
                 updateCumulativeProfitChart(chartData);
            }

            function getSortValue(trade, column) {
                 const invested = trade.total_quantity * trade.buy_price_avg;
                 const isClosed = !!trade.sell_price && !!trade.sell_date;
                 const currentPrice = (typeof trade.current_price !== 'undefined') ? trade.current_price : null;
                 const sellPrice = isClosed ? trade.sell_price : null;

                 let sortReturnedValue = null;
                 let sortPnlValue = null;
                 let sortPnlPercentValue = null;

                 if (isClosed) {
                     sortReturnedValue = trade.total_quantity * sellPrice;
                     sortPnlValue = sortReturnedValue - invested;
                     if (invested > 0) sortPnlPercentValue = (sortPnlValue / invested) * 100;
                 } else {
                      if (currentPrice !== null) {
                        sortReturnedValue = trade.total_quantity * currentPrice;
                        sortPnlValue = sortReturnedValue - invested;
                        if (invested > 0) sortPnlPercentValue = (sortPnlValue / invested) * 100;
                     }
                 }

                 let buyDateTime = null;
                 try {
                     buyDateTime = new Date(trade.buy_date).getTime();
                     if (isNaN(buyDateTime)) buyDateTime = null;
                 } catch {
                     buyDateTime = null;
                 }
                 let sellDateTime = null;
                 if (isClosed && trade.sell_date) {
                    try {
                        sellDateTime = new Date(trade.sell_date).getTime();
                        if (isNaN(sellDateTime)) sellDateTime = null;
                    } catch {
                        sellDateTime = null;
                    }
                 }


                 switch (column) {
                    case 'symbol': return trade.symbol;
                    case 'buy_price': return trade.buy_price_avg;
                    case 'sell_price': return sellPrice;
                    case 'current_price': return currentPrice;
                    case 'quantity': return trade.total_quantity;
                    case 'invested': return invested;
                    case 'returned': return sortReturnedValue;
                    case 'pnl': return sortPnlValue;
                    case 'pnl_percent': return sortPnlPercentValue;
                    case 'duration': // Sort by raw milliseconds for consistent ordering
                         if (isClosed && sellDateTime !== null && buyDateTime !== null) {
                            return sellDateTime - buyDateTime;
                         } else if (!isClosed && buyDateTime !== null) {
                             // For open trades, sort by duration up to now
                             return new Date().getTime() - buyDateTime;
                         }
                         return null;
                    case 'status': return isClosed ? 1 : 0; // 1 for closed, 0 for open (or vice-versa based on desired default sort)
                    case 'buy_date': return buyDateTime; // Use timestamp of buy_date
                    default: return null;
                }
            }


             function handleEditTradeClick(event) {
                 const button = event.currentTarget;
                 const symbol = button.dataset.symbol;
                 const buyDate = button.dataset.buyDate;

                 if (!symbol || !buyDate) {
                     showToast('Error: Missing trade identifier.', 'error');
                     return;
                 }

                 const trade = window.tradeData.find(t => t.symbol === symbol && t.buy_date === buyDate);

                 if (!trade) {
                     showToast('Error: Could not find trade data.', 'error');
                     return;
                 }
                 if (trade.sell_price && trade.sell_date) {
                     showToast('Error: Cannot edit a closed trade.', 'error');
                     return;
                 }

                 Swal.fire({
                     title: `Edit Trade: ${symbol}`,
                     html: `
                         <div class="input-pair">
                           <label for="swal-input-price">Buy Price:</label>
                           <input id="swal-input-price" class="swal2-input" type="number" step="0.01" min="0.01" value="${trade.buy_price_avg.toFixed(2)}">
                         </div>
                         <div class="input-pair">
                           <label for="swal-input-quantity">Quantity:</label>
                           <input id="swal-input-quantity" class="swal2-input" type="number" step="1" min="1" value="${trade.total_quantity}">
                         </div>
                     `,
                     confirmButtonText: 'Update Trade',
                     showCancelButton: true,
                     focusConfirm: false,
                     preConfirm: () => {
                         const priceInput = Swal.getPopup().querySelector('#swal-input-price');
                         const quantityInput = Swal.getPopup().querySelector('#swal-input-quantity');
                         const newPrice = parseFloat(priceInput.value);
                         const newQuantity = parseInt(quantityInput.value);

                         if (isNaN(newPrice) || newPrice <= 0) {
                             Swal.showValidationMessage(`Please enter a valid positive buy price`);
                             priceInput.focus();
                             return false;
                         }
                         if (isNaN(newQuantity) || newQuantity <= 0 || !Number.isInteger(newQuantity)) {
                             Swal.showValidationMessage(`Please enter a valid positive whole number for quantity`);
                             quantityInput.focus();
                             return false;
                         }
                         return { newPrice: newPrice, newQuantity: newQuantity };
                     }
                 }).then((result) => {
                     if (result.isConfirmed && result.value) {
                         const { newPrice, newQuantity } = result.value;
                         Swal.showLoading();
                         fetch('/api/trade_history/update', {
                             method: 'POST',
                             headers: { 'Content-Type': 'application/json' },
                             body: JSON.stringify({
                                 symbol: symbol,
                                 buy_date: buyDate,
                                 new_price: newPrice,
                                 new_quantity: newQuantity
                             })
                         })
                         .then(response => response.json().then(data => ({ ok: response.ok, data })))
                         .then(({ ok, data }) => {
                             if (ok && data.status === 'success') {
                                 loadData(); // Reload all data to reflect changes
                                 showToast(`Trade ${symbol} updated successfully!`, 'success');
                                 Swal.close();
                             } else {
                                 throw new Error(data.error || 'Failed to update trade.');
                             }
                         })
                         .catch(error => {
                             console.error("Error updating trade:", error);
                             Swal.update({
                                  icon: 'error',
                                  title: 'Update Failed',
                                  text: error.message,
                                  showConfirmButton: true,
                                  confirmButtonText: 'OK'
                             });
                             Swal.hideLoading();
                         });
                     }
                 });
             }
            let chartConfigDefaults = {
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: { mode: 'index', intersect: false },
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: { color: 'var(--chart-tick-color)' },
                            grid: { color: 'var(--chart-grid-color)', borderColor: 'var(--chart-grid-color)' }
                        },
                        x: {
                            ticks: { color: 'var(--chart-tick-color)', autoSkip: true, maxTicksLimit: 10 },
                            grid: { display: false, borderColor: 'var(--chart-grid-color)' }
                        }
                    },
                    plugins: {
                        legend: { labels: { color: 'var(--chart-label-color)' } },
                        tooltip: {
                             backgroundColor: 'var(--card-bg)',
                             titleColor: 'var(--body-color)',
                             bodyColor: 'var(--body-color)',
                             borderColor: 'var(--card-border)',
                             borderWidth: 1,
                             padding: 10,
                             cornerRadius: 6
                        }
                    },
                    elements: {
                        line: { tension: 0.3 },
                        point: { radius: 2 }
                    },
                    animation: {
                        duration: 500,
                        easing: 'easeInOutQuad'
                    }
                }
            };

            function updateChartColors(chart) {
                if (!chart) return;
                try {
                    const style = getComputedStyle(document.documentElement);
                    chart.options.scales.y.ticks.color = style.getPropertyValue('--chart-tick-color').trim();
                    chart.options.scales.y.grid.color = style.getPropertyValue('--chart-grid-color').trim();
                    chart.options.scales.y.grid.borderColor = style.getPropertyValue('--chart-grid-color').trim();
                    chart.options.scales.x.ticks.color = style.getPropertyValue('--chart-tick-color').trim();
                    chart.options.scales.x.grid.borderColor = style.getPropertyValue('--chart-grid-color').trim();
                    chart.options.plugins.legend.labels.color = style.getPropertyValue('--chart-label-color').trim();
                    chart.options.plugins.tooltip.backgroundColor = style.getPropertyValue('--card-bg').trim();
                    chart.options.plugins.tooltip.titleColor = style.getPropertyValue('--body-color').trim();
                    chart.options.plugins.tooltip.bodyColor = style.getPropertyValue('--body-color').trim();
                    chart.options.plugins.tooltip.borderColor = style.getPropertyValue('--card-border').trim();
                    chart.update('none');
                 } catch (e) {
                     console.error("Error updating chart colors:", e);
                 }
            }


            function updateChart(data) {
                const chartCanvas = document.getElementById('profit-chart');
                const loader = document.getElementById('profit-chart-loader');
                const closedTradesData = data.filter(trade => trade.sell_date && trade.sell_price);

                if (!closedTradesData || closedTradesData.length === 0) {
                     loader.innerHTML = '<p class="text-muted text-center pt-5">No closed trade data for chart based on current filters.</p>';
                     loader.style.display = 'block';
                     chartCanvas.style.display = 'none';
                     if (window.myChart) {
                         window.myChart.destroy();
                         window.myChart = null;
                     }
                     return;
                }

                loader.style.display = 'block';
                chartCanvas.style.display = 'none';
                chartCanvas.style.opacity = 0;
                const CHARGE_PER_TRADE = 12; // Consistent with transactionCharges calculation elsewhere

                const dailyAggregates = closedTradesData.reduce((acc, trade) => {
                    try {
                        const tradeDate = new Date(trade.sell_date).toISOString().split('T')[0];
                        const grossPnl = (trade.sell_price - trade.buy_price_avg) * trade.total_quantity;
                        
                        if (!acc[tradeDate]) {
                            acc[tradeDate] = { grossPnl: 0, tradeCount: 0 };
                        }
                        acc[tradeDate].grossPnl += grossPnl;
                        acc[tradeDate].tradeCount += 1;
                    } catch (e) {
                        console.warn("Skipping trade in chart due to date error:", e, trade);
                    }
                    return acc;
                }, {});

                const labels = Object.keys(dailyAggregates).sort((a, b) => a.localeCompare(b));

                const netPnlData = labels.map(date => {
                    const grossPnl = dailyAggregates[date].grossPnl;
                    const charges = dailyAggregates[date].tradeCount * CHARGE_PER_TRADE;
                    return grossPnl - charges;
                });

                const chargesData = labels.map(date => {
                    return dailyAggregates[date].tradeCount * CHARGE_PER_TRADE;
                });

                if (window.myChart) window.myChart.destroy();

                const ctx = chartCanvas.getContext('2d');
                window.myChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: 'Net P&L',
                                data: netPnlData,
                                backgroundColor: netPnlData.map(p => p >= 0 ? 'rgba(34, 197, 94, 0.7)' : 'rgba(239, 68, 68, 0.7)'), // Green for profit, Red for loss
                                borderColor: netPnlData.map(p => p >= 0 ? 'rgba(34, 197, 94, 1)' : 'rgba(239, 68, 68, 1)'),
                                borderWidth: 1
                            },
                            {
                                label: 'Charges',
                                data: chargesData,
                                backgroundColor: 'rgba(54, 162, 235, 0.7)', // Blue for charges
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        ...chartConfigDefaults.options,
                        scales: {
                            x: {
                                ...(chartConfigDefaults.options.scales?.x || {}),
                                stacked: true
                            },
                            y: {
                                ...(chartConfigDefaults.options.scales?.y || {}),
                                stacked: true
                            }
                        }
                    }
                });

                updateChartColors(window.myChart);
                requestAnimationFrame(() => {
                    loader.style.display = 'none';
                    chartCanvas.style.display = 'block';
                    chartCanvas.style.animation = 'fadeInAnimation 0.5s ease-in-out forwards';
                    chartCanvas.style.opacity = 1;
                });
            }


            function updateCumulativeProfitChart(data) {
                const chartCanvas = document.getElementById('cumulative-profit-chart');
                const loader = document.getElementById('cumulative-profit-chart-loader');
                 const closedTrades = data.filter(trade => trade.sell_date && trade.sell_price)
                    .map(trade => {
                        try {
                             trade.parsedSellDate = new Date(trade.sell_date).getTime();
                             if (isNaN(trade.parsedSellDate)) throw new Error("Invalid date");
                             return trade;
                        } catch (e) {
                             console.warn("Skipping trade in cumulative chart due to date error:", e, trade);
                             return null;
                        }
                    })
                    .filter(trade => trade !== null)
                    .sort((a, b) => a.parsedSellDate - b.parsedSellDate);


                if (closedTrades.length === 0) {
                     loader.innerHTML = '<p class="text-muted text-center pt-5">No closed trade data for chart based on current filters.</p>';
                     loader.style.display = 'block';
                     chartCanvas.style.display = 'none';
                     if (window.myCumulativeChart) {
                         window.myCumulativeChart.destroy();
                         window.myCumulativeChart = null;
                     }
                     return;
                 }

                loader.style.display = 'block';
                chartCanvas.style.display = 'none';
                chartCanvas.style.opacity = 0;

                const tradeCharge = 12;
                const dailyNetCumulative = {};
                 let runningTotal = 0;
                 closedTrades.forEach(trade => {
                     const date = new Date(trade.parsedSellDate).toISOString().split('T')[0];
                     const realizedPnl = (trade.sell_price - trade.buy_price_avg) * trade.total_quantity;
                     runningTotal += (realizedPnl - tradeCharge);
                     dailyNetCumulative[date] = runningTotal;
                 });

                 const consolidatedLabels = Object.keys(dailyNetCumulative).sort((a, b) => a.localeCompare(b));
                 const consolidatedData = consolidatedLabels.map(date => dailyNetCumulative[date]);

                if (window.myCumulativeChart) window.myCumulativeChart.destroy();

                const ctx = chartCanvas.getContext('2d');
                window.myCumulativeChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: consolidatedLabels,
                        datasets: [{
                            label: 'Cumulative Net Profit',
                            data: consolidatedData,
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            fill: true,
                            borderWidth: 2
                        }]
                    },
                     options: { ...chartConfigDefaults.options }
                });

                updateChartColors(window.myCumulativeChart);
                 requestAnimationFrame(() => {
                    loader.style.display = 'none';
                    chartCanvas.style.display = 'block';
                    chartCanvas.style.animation = 'fadeInAnimation 0.5s ease-in-out forwards';
                    chartCanvas.style.opacity = 1;
                 });
            }

            function showLoadingError(message) {
                const tbody = document.getElementById('trade-history-body');
                const numCols = 12;
                if (tbody) {
                     tbody.innerHTML = `<tr><td colspan="${numCols}" class="text-center py-4 text-danger">${message}</td></tr>`;
                }
                 const profitLoader = document.getElementById('profit-chart-loader');
                 const cumulativeLoader = document.getElementById('cumulative-profit-chart-loader');
                 if (profitLoader) profitLoader.innerHTML = `<p class="text-danger text-center pt-5">${message}</p>`;
                 if (cumulativeLoader) cumulativeLoader.innerHTML = `<p class="text-danger text-center pt-5">${message}</p>`;

                 const profitChart = document.getElementById('profit-chart');
                 const cumulativeChart = document.getElementById('cumulative-profit-chart');
                 if (profitChart) profitChart.style.display = 'none';
                 if (cumulativeChart) cumulativeChart.style.display = 'none';
                 document.querySelectorAll('.stat-card .skeleton, .stock-performance-card .skeleton').forEach(el => el.remove());
                 [
                     'net-available-funds', 'active-investment', 'unrealized-pnl',
                     'realized-pnl', 'win-rate', 'total-trades', 'total-open-trades',
                     'consolidated-net-profit', 'consolidated-closed-investment',
                     'consolidated-roi', 'consolidated-total-closed', 'transaction-charges-display',
                     'summary-total-trades', 'summary-winning-trades', 'summary-losing-trades',
                     'summary-avg-profit', 'summary-avg-loss', 'summary-largest-win',
                     'summary-largest-loss', 'summary-avg-pnl-percent', 'summary-avg-holding-days',
                     'summary-profit-factor'
                ].forEach(id => {
                     const el = document.getElementById(id);
                     if (el) {
                        if (id === 'transaction-charges-display') {
                            el.textContent = 'Charges: -';
                        } else {
                            el.textContent = '-';
                        }
                     }
                 });

                Swal.fire({
                    icon: 'error', title: 'Loading Error', text: message,
                });
            }

             function renderTableSkeletons(tbodyId, cols, rows = 3) {
                 const tbody = document.getElementById(tbodyId);
                 if (!tbody) return;
                 let skeletonHTML = '';
                 const defaultCell = `<td><span class="skeleton skeleton-text-short mx-auto"></span></td>`;
                 const badgeCell = `<td><span class="skeleton skeleton-badge"></span></td>`;
                 const statusBadgeCell = `<td><span class="skeleton skeleton-status-badge mx-auto"></span></td>`;
                 const actionBtnCell = `<td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>`;

                 for (let i = 0; i < rows; i++) {
                     skeletonHTML += `<tr class="skeleton-row">`;
                     for (let j = 0; j < cols; j++) {
                         if (j === 0) skeletonHTML += badgeCell;
                         else if (j === cols - 2) skeletonHTML += statusBadgeCell;
                         else if (j === cols - 1) skeletonHTML += actionBtnCell;
                         else skeletonHTML += defaultCell;
                     }
                     skeletonHTML += `</tr>`;
                 }
                 tbody.innerHTML = skeletonHTML;
             }
              function showToast(message, type = 'info') {
                  Swal.fire({
                      toast: true,
                      position: 'top-end',
                      icon: type,
                      title: message,
                      showConfirmButton: false,
                      timer: 3000,
                      timerProgressBar: true,
                  });
              }

            loadData();
        });
    </script>
<script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"935a8b76df19e22f","version":"2025.4.0-1-g37f21b1","r":1,"token":"79c53ece347a4075a77208d2660a9019","serverTiming":{"name":{"cfExtPri":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}}}' crossorigin="anonymous"></script>
</body>
</html>