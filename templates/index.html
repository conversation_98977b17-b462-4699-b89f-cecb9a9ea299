<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finvasia</title>
    <style>
        /* Drawer styles */
        .config-drawer {
            position: fixed;
            top: 0;
            left: -350px; /* Initially hidden */
            width: 350px;
            height: 100%;
            background-color: var(--drawer-bg, #1e293b);
            border-left: 1px solid var(--drawer-border, #334155);
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
            z-index: 1060; /* Higher than other elements */
            transition: left 0.5s ease-in-out;
            padding: 20px;
            overflow-y: auto;
            color: var(--body-color);
            display: block; /* Ensure it's displayed */
        }
        .config-drawer.open {
            left: 0;
            display: block; /* Ensure it's displayed when open */
            visibility: visible; /* Make sure it's visible */
        }
        .config-drawer h5 {
            margin-top: 0;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 10px;
        }
        .config-drawer .form-label {
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        .config-drawer .input-group {
            margin-bottom: 15px;
        }
        .config-drawer .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        .threshold-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--input-border);
        }
        .threshold-item:last-child {
            border-bottom: none;
        }
        .threshold-values {
            flex-grow: 1;
        }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.5/dist/sweetalert2.min.css">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='Breakout.ico') }}">
     <link rel="icon" type="image/png" href="https://shoonya.com/static/img/favicons/favicon-32x32.7444e68.png" sizes="32x32">


    <style>
        :root {
            --primary-rgb: 99, 102, 241;
            --primary: #6366f1;
            --primary-hover: #4f46e5;
            --success: #22c55e;
            --success-hover: #16a34a;
            --warning: #f59e0b;
            --danger: #ef4444;
            --danger-hover: #dc2626;
            --info: #3abff8;
            --muted: #64748b;
            --light: #f8fafc;
            --dark: #0f172a;
            --body-bg: var(--light);
            --body-color: #334155;
            --card-bg: #ffffff;
            --card-border: #e2e8f0;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.03), 0 1px 2px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.08);
            --table-header-bg: #f1f5f9;
            --table-row-hover-bg: #f8fafc;
            --input-bg: #ffffff;
            --input-border: #cbd5e1;
            --input-focus-border: var(--primary);
            --input-focus-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), .25);
            --skeleton-bg: #e2e8f0;
            --skeleton-animation: skeleton-loading 1.5s infinite ease-in-out;
            --drawer-bg: var(--card-bg);
            --drawer-border: var(--card-border);
            --border-radius: 0.75rem;
            --transition-speed: 0.2s;
            --transition-ease: ease-in-out;
            --highlight-bg-light: rgba(34, 197, 94, 0.1);
            --highlight-bg-light-hover: rgba(34, 197, 94, 0.15);
            --highlight-bg-dark: rgba(74, 222, 128, 0.15);
            --highlight-bg-dark-hover: rgba(74, 222, 128, 0.2);
        }

        [data-bs-theme="dark"] {
            --muted: #94a3b8;
            --body-bg: var(--dark);
            --body-color: #cbd5e1;
            --card-bg: #1e293b;
            --card-border: #334155;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.2);
            --card-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.25);
            --table-header-bg: #293548;
            --table-row-hover-bg: #243147;
            --input-bg: #293548;
            --input-border: #475569;
            --input-focus-border: var(--primary);
            --skeleton-bg: #334155;
            --drawer-bg: #1e293b;
            --drawer-border: #334155;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--body-bg);
            color: var(--body-color);
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease);
            font-size: 0.95rem;
        }

        .card, .stat-card {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease), background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
            margin-bottom: 1.5rem;
        }
        .card:hover, .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--card-shadow-hover);
        }
         .card-body.p-0 {
            padding: 0 !important;
        }
        .stat-card {
            padding: 1.25rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            overflow: hidden;
        }
        .stat-card .stat-label {
             color: var(--muted);
             font-size: 0.85rem;
             margin-bottom: 0.25rem;
             line-height: 1.3;
        }
        .stat-card .stat-value {
            font-size: 1.75rem;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 0.25rem;
            word-break: break-word;
        }
        .stat-card .stat-subtext {
            font-size: 0.8rem;
            color: var(--muted);
            line-height: 1.3;
        }
        .stat-subtext span[id^="holdings-day-pnl"] {
            font-weight: 500;
        }

        .card-header {
            background-color: var(--table-header-bg);
            border-bottom: 1px solid var(--card-border);
            padding: 0.85rem 1.25rem;
             border-radius: var(--border-radius) var(--border-radius) 0 0;
            transition: background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
         .card-header h6 { font-weight: 600; margin-bottom: 0; }

        .profit-positive, .profit { color: var(--success); }
        .profit-negative, .loss { color: var(--danger); }
        .metric-small { font-size: 0.85em; color: var(--muted); }

        .common-table-container {
             overflow-x: auto;
             border: 1px solid var(--card-border);
             border-radius: var(--border-radius);
             margin: -1px;
             transition: border-color var(--transition-speed) var(--transition-ease);
             border-top-left-radius: 0;
             border-top-right-radius: 0;
             border-bottom-left-radius: var(--border-radius);
             border-bottom-right-radius: var(--border-radius);

        }
        .card:has(.card-header) .common-table-container {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
        .card:not(:has(.card-header)) .card-body > .common-table-container:first-child {
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
        }

        .common-table {
            width: 100%;
            min-width: 750px;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 0.9rem;
        }

        .common-table thead th {
            background-color: var(--table-header-bg);
            border-bottom: 2px solid var(--card-border);
            color: var(--body-color);
            font-weight: 600;
            padding: 0.75rem 0.85rem;
            white-space: nowrap;
            vertical-align: middle;
            transition: background-color var(--transition-speed) var(--transition-ease);
            position: sticky;
            top: 0;
            z-index: 1;
            cursor: pointer;
            user-select: none;
            text-align: center;
        }
         .common-table-container:not([style*="border-top-left-radius: 0px"]) thead th:first-child { border-top-left-radius: 0; }
         .common-table-container:not([style*="border-top-right-radius: 0px"]) thead th:last-child { border-top-right-radius: 0; }


        .common-table tbody tr {
            transition: background-color var(--transition-speed) var(--transition-ease), opacity 0.3s var(--transition-ease);
            opacity: 1;
         }
        .common-table tbody tr:hover {
            background-color: var(--table-row-hover-bg);
        }
        .common-table td {
            padding: 0.75rem 0.85rem;
            vertical-align: middle;
            border-top: 1px solid var(--card-border);
            text-align: center;
            white-space: nowrap;
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
        .common-table tbody tr:last-child td:first-child { border-bottom-left-radius: var(--border-radius); }
        .common-table tbody tr:last-child td:last-child { border-bottom-right-radius: var(--border-radius); }

        .common-table th.asc::after, .common-table th.desc::after {
            content: '';
            display: inline-block;
            width: 0; height: 0;
            margin-left: 8px;
            vertical-align: middle;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
        .common-table th.asc::after { border-bottom: 5px solid var(--primary); }
        .common-table th.desc::after { border-top: 5px solid var(--primary); }

        /* Alignment overrides */
        .common-table th.col-symbol, .common-table td.col-symbol,
        .common-table th.col-company, .common-table td.col-company,
        .common-table td.col-symbol-link {
            text-align: left;
            padding-left: 1rem;
            white-space: nowrap;
        }
         .common-table td.col-company { min-width: 150px; }
        .common-table th:last-child, .common-table td:last-child { padding-right: 1rem; }
        .common-table td.actions-cell { white-space: nowrap; min-width: 100px; vertical-align: middle; }
        .common-table td.instant-buy-cell { min-width: 150px; vertical-align: middle; }

        .highlight-identified {
            background-color: var(--highlight-bg-light);
        }
        .highlight-identified:hover {
             background-color: var(--highlight-bg-light-hover);
        }
        [data-bs-theme="dark"] .highlight-identified {
            background-color: var(--highlight-bg-dark);
        }
         [data-bs-theme="dark"] .highlight-identified:hover {
             background-color: var(--highlight-bg-dark-hover);
         }

        .stock-badge {
            background: rgba(var(--primary-rgb), 0.1);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid rgba(var(--primary-rgb), 0.2);
            text-decoration: none;
            display: inline-block;
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .stock-badge:hover { background: rgba(var(--primary-rgb), 0.15); color: var(--primary-hover); }

        /* UPDATED Flashing Animation */
        .stock-badge.flashing {
            animation: flashBackground 0.8s infinite alternate ease-in-out; /* Faster duration */
        }
        @keyframes flashBackground {
            from {
                background-color: rgba(var(--primary-rgb), 0.1);
                border-color: rgba(var(--primary-rgb), 0.2);
            }
            to {
                background-color: rgba(var(--danger), 0.25); /* Use danger color, slightly more opaque */
                border-color: rgba(var(--danger), 0.4);    /* Use danger color for border, slightly more opaque */
            }
        }
        /* END UPDATED Flashing Animation */

        .header-icon { color: var(--primary); }
        .purchased-icon { color: var(--success); font-size: 0.9em; vertical-align: middle; margin-left: 4px;}
        .paper-trade-icon { color: var(--info); font-size: 0.9em; vertical-align: middle; margin-left: 4px;}

        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all var(--transition-speed) var(--transition-ease);
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.4rem;
            border: 1px solid transparent;
            line-height: 1.5;
            min-height: calc(1.5em + 1rem + 2px);
            cursor: pointer;
            white-space: nowrap; /* Prevent text wrapping */
        }
        .btn:hover:not(:disabled) { transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0,0,0,0.08); }
        .btn:disabled { opacity: 0.65; cursor: not-allowed; transform: none; box-shadow: none; }
        .btn i { line-height: 1; vertical-align: middle; }
        .btn span { vertical-align: middle; }

        .btn-primary { background-color: var(--primary); border-color: var(--primary); color: white; }
        .btn-primary:hover:not(:disabled) { background-color: var(--primary-hover); border-color: var(--primary-hover); }
        .btn-success { background-color: var(--success); border-color: var(--success); color: white; }
        .btn-success:hover:not(:disabled) { background-color: var(--success-hover); border-color: var(--success-hover); }
        .btn-danger { background-color: var(--danger); border-color: var(--danger); color: white; }
        .btn-danger:hover:not(:disabled) { background-color: var(--danger-hover); border-color: var(--danger-hover); }
        .btn-outline-secondary { border-color: var(--card-border); color: var(--muted); }
        .btn-outline-secondary:hover:not(:disabled) { background-color: var(--table-row-hover-bg); border-color: var(--card-border); color: var(--body-color); }
        .btn-outline-primary { color: var(--primary); border-color: var(--primary); }
        .btn-outline-primary:hover:not(:disabled) { background-color: rgba(var(--primary-rgb), 0.1); }
        .btn-sm { padding: 0.35rem 0.8rem; font-size: 0.875rem; gap: 0.3rem; min-height: calc(1.5em + 0.7rem + 2px); }

        .form-control, .form-select, .form-check-input {
            background-color: var(--input-bg);
            border: 1px solid var(--input-border);
            color: var(--body-color);
            border-radius: 0.5rem;
            transition: border-color var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease), background-color var(--transition-speed) var(--transition-ease);
        }
        .form-control:focus, .form-select:focus {
            border-color: var(--input-focus-border);
            outline: 0;
            box-shadow: var(--input-focus-shadow);
            background-color: var(--input-bg);
            color: var(--body-color);
        }
        .form-check-input { border: 1px solid var(--input-border); cursor: pointer;}
        .form-check-input:checked { background-color: var(--primary); border-color: var(--primary); }
        .form-check-input:focus { box-shadow: var(--input-focus-shadow); }
        .form-check-label { cursor: pointer; }
        .input-group .form-control { border-radius: 0.5rem 0 0 0.5rem; }
        .input-group .btn { border-radius: 0 0.5rem 0.5rem 0; margin-left: -1px; }
        label { font-weight: 500; margin-bottom: 0.3rem; }
        .form-control.text-center { text-align: center; }

        .refresh-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(var(--primary-rgb), 0.05);
            padding: 5px 10px;
            border-radius: 6px;
            border: 1px solid rgba(var(--primary-rgb), 0.1);
            font-size: 0.85em;
            color: var(--muted);
            transition: background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .refresh-dial {
            width: 16px; height: 16px;
            border: 2px solid var(--primary);
            border-radius: 50%;
            border-top-color: transparent;
            animation: rotate 1s linear infinite;
            transition: opacity 0.3s ease-in-out;
            opacity: 1;
        }
        .refresh-dial.hidden { opacity: 0; }
        #positions-refresh-timer, #holdings-refresh-timer { min-width: 10px; text-align: right; font-weight: 500; }

        @keyframes rotate { to { transform: rotate(360deg); } }

        #notifications-container { position: fixed; top: 20px; right: 20px; z-index: 1050; width: 320px; max-width: 90vw; }
        .order-result {
            padding: 1rem; border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative; margin-bottom: 1rem;
            border: 1px solid transparent;
            opacity: 0; animation: toast-in 0.5s cubic-bezier(0.215, 0.610, 0.355, 1.000) forwards;
            background-color: var(--card-bg);
            color: var(--body-color);
            transform: translateX(100%);
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .order-result.success { border-left: 4px solid var(--success); }
        .order-result.error { border-left: 4px solid var(--danger); }
        .order-result.info { border-left: 4px solid var(--info); }
        .order-result.warning { border-left: 4px solid var(--warning); }
        .order-result .fas { margin-right: 0.5rem; }
        .notification-close {
            position: absolute; top: 8px; right: 8px;
            background: none; border: none; cursor: pointer;
            font-size: 1.2em; color: var(--muted); opacity: 0.7;
            transition: opacity var(--transition-speed) ease;
            padding: 0; line-height: 1;
        }
        .notification-close:hover { opacity: 1; }
        @keyframes toast-in {
            0% { opacity: 0; transform: translateX(100%); }
            100% { opacity: 1; transform: translateX(0); }
        }
        @keyframes toast-out {
            0% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }

        .drawer {
            position: fixed;
            bottom: 0;
            width: 95%; max-width: 380px; height: 75vh; max-height: 500px;
            background-color: var(--drawer-bg);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.15);
            z-index: 1040;
            transform: translateY(100%);
            transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1), background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
            overflow: hidden;
            border-top: 1px solid var(--drawer-border);
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            display: flex;
            flex-direction: column;
        }
        .drawer.open { transform: translateY(0); }
        .drawer-header {
             padding: 1rem 1.25rem;
             border-bottom: 1px solid var(--drawer-border);
             display: flex; justify-content: space-between; align-items: center;
             transition: border-color var(--transition-speed) var(--transition-ease);
             flex-shrink: 0;
        }
         .drawer-header h5 { margin-bottom: 0; font-weight: 600; }
         .drawer-body { padding: 1.25rem; flex-grow: 1; overflow-y: auto; }
         .drawer-footer { padding: 0.75rem 1.25rem; border-top: 1px solid var(--drawer-border); transition: border-color var(--transition-speed) var(--transition-ease); flex-shrink: 0; }

        #notification-drawer {
    right: 20px !important;
    bottom: 20px !important;
    left: auto !important;
    top: auto !important;
    width: 350px !important;
    max-width: 90vw !important;
    height: auto !important;
    max-height: 70vh !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 0 4px 24px rgba(0,0,0,0.18) !important;
    transform: translateY(120%);
    transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1), background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
    z-index: 1060 !important;
    display: flex;
    flex-direction: column;
}
#notification-drawer.open {
    transform: translateY(0);
}

        #trailing-logs-drawer {
    left: 20px !important;
    bottom: 20px !important;
    right: auto !important;
    top: auto !important;
    width: 350px !important;
    max-width: 90vw !important;
    height: auto !important;
    max-height: 70vh !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 0 4px 24px rgba(0,0,0,0.18) !important;
    transform: translateY(120%);
    transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1), background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
    z-index: 1060 !important;
    display: flex;
    flex-direction: column;
}
#trailing-logs-drawer.open {
    transform: translateY(0);
}

        .drawer-toggle {
            position: fixed; bottom: 15px; z-index: 1045;
            cursor: pointer;
            background-color: var(--primary); color: white;
            border: none; padding: 0.6rem 1rem; border-radius: 50px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: background-color var(--transition-speed) ease, transform 0.2s ease;
        }
        .drawer-toggle:hover { background-color: var(--primary-hover); transform: scale(1.05); }
        #notification-drawer-toggle { right: 15px; z-index: 9999 !important; }
        #trailing-logs-toggle { left: 15px; }

        .notification-date, .trailing-log-timestamp { font-size: 0.8em; color: var(--muted); margin-bottom: 0.25rem; font-weight: 500; }
        .drawer-notification, .trailing-log-entry {
            padding: 0.85rem 0;
            border-bottom: 1px solid var(--drawer-border);
            transition: border-color var(--transition-speed) var(--transition-ease);
            opacity: 0;
            animation: fadeInAnimation 0.4s ease-out forwards;
        }
        .drawer-notification:last-child, .trailing-log-entry:last-child { border-bottom: none; }
        .notification-time { font-size: 0.8em; color: var(--muted); margin-top: 3px; }
        .market-closed-msg { color: var(--warning); font-weight: 500; }
        #clear-notifications, #clear-trailing-logs { width: 100%; }
        .drawer-body .text-muted { padding-top: 1rem; }

        .skeleton {
            background-color: var(--skeleton-bg);
            border-radius: 0.25rem;
            position: relative;
            overflow: hidden;
            display: block;
            width: 100%;
            min-height: 1em;
            transition: background-color var(--transition-speed) var(--transition-ease);
        }
        .skeleton::before {
            content: ''; position: absolute; top: 0; left: -150%; width: 150%; height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: var(--skeleton-animation);
        }
        [data-bs-theme="dark"] .skeleton::before {
             background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
        }
        @keyframes skeleton-loading {
            0% { left: -150%; }
            100% { left: 150%; }
        }

        .skeleton-text { height: 1em; margin-bottom: 0.5em; }
        .skeleton-text-short { width: 60%; height: 1em; margin-bottom: 0.5em; }
        .skeleton-h3 { width: 50%; height: 1.75rem; margin-bottom: 0.5rem; border-radius: 0.375rem; }
        .skeleton-badge { width: 80px; height: 24px; display: inline-block; border-radius: 6px; vertical-align: middle; }
        .skeleton-btn { width: 70px; height: calc(1.5em + 0.7rem + 2px); display: inline-block; border-radius: 0.5rem; vertical-align: middle; }
        .skeleton-input { height: calc(1.5em + 0.9rem + 2px); border-radius: 0.5rem; }
        .skeleton-checkbox { width: 18px; height: 18px; border-radius: 0.25rem; display: inline-block; vertical-align: middle;}
        .skeleton-label { width: 100px; height: 1em; margin-bottom: 0.3rem; }

        .stat-card .stat-label .skeleton { width: 70%; height: 0.85rem; margin-bottom: 0.5rem;}
        .stat-card .stat-value .skeleton { width: 50%; height: 1.75rem; margin-bottom: 0.5rem;}
        .stat-card .stat-subtext .skeleton { width: 80%; height: 0.8rem;}
        .skeleton-instant-buy {
            display: flex; gap: 1; justify-content: center; align-items: center; width: 100%;
        }

        .content-fade-in {
            opacity: 0;
            animation: fadeInAnimation 0.5s ease-in-out forwards;
        }
        @keyframes fadeInAnimation {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .spinner-border-sm { width: 1em; height: 1em; border-width: 0.15em; }
        .btn .spinner-border { display: inline-block; vertical-align: middle; }


        @media (max-width: 992px) {
             .top-nav { flex-direction: column; align-items: flex-start !important; }
             .top-nav > div:last-child { margin-top: 0.5rem; align-self: flex-start; }
             #identified-stocks-controls { flex-direction: column; align-items: stretch; gap: 1rem; }
             #identified-stocks-controls > div { width: 100%; }
             .holdings-controls { flex-direction: column; align-items: stretch !important; gap: 1rem; }
             .holdings-controls .input-group { width: 100% !important; }
             .holdings-controls .percentage-inputs-container { justify-content: flex-start !important; }
             .common-table { min-width: 700px; }
        }
         @media (max-width: 768px) {
            body { font-size: 0.9rem; }
            .container-xl { padding-left: 0.75rem; padding-right: 0.75rem; }
            .stat-card { padding: 1rem; min-height: 100px; }
            .stat-card .stat-value { font-size: 1.5rem; }
            .common-table { min-width: 600px; }
            .common-table th, .common-table td { padding: 0.6rem 0.5rem; white-space: nowrap; font-size: 0.85rem;}
             .common-table th.col-symbol, .common-table td.col-symbol,
             .common-table th.col-company, .common-table td.col-company,
             .common-table td.col-symbol-link { padding-left: 0.75rem; }
             .common-table th:last-child, .common-table td:last-child { padding-right: 0.75rem; }
             .drawer { width: 100%; max-width: 100%; border-radius: var(--border-radius) var(--border-radius) 0 0; max-height: 60vh; }
            .drawer-toggle { padding: 0.5rem 0.9rem; position: fixed; z-index: 2147483647 !important; }
            #notification-drawer-toggle { right: 10px; bottom: 10px; }
            #trailing-logs-toggle { left: 10px; bottom: 10px; /* z-index handled by .drawer-toggle */ }
            .common-table td.instant-buy-cell { min-width: 120px; }
             .instant-buy-group-wrapper { justify-content: center; }
        }
        @media (max-width: 576px) {
            #investment-amount { width: 100% !important; }
            #identified-stocks-controls .form-check { justify-content: flex-start; margin-top: 0.5rem; }
            .stat-card { min-height: 90px; }
            .stat-card .stat-value { font-size: 1.3rem; }
            .row.g-3 > * { padding-left: 0.5rem; padding-right: 0.5rem; }
            .common-table { min-width: 500px; }
             .drawer { max-height: 70vh; }
             .top-nav .btn.hide-text-mobile span { display: none; }
             .top-nav .btn { padding: 0.5rem 0.7rem; }
             .top-nav .btn.hide-text-mobile i { margin-right: 0 !important; }
        }

    </style>
</head>

<body>
    <!-- Config Drawer Button -->


    <!-- Config Drawer -->
    <div id="configDrawer" class="config-drawer">
        <h5>Configuration Settings</h5>
        
        <div class="input-group input-group-sm mb-3">
            <label for="sell-percentage" class="input-group-text">Sell Trigger %</label>
            <input type="number" id="sell-percentage" min="0.1" step="0.1" class="form-control text-center" placeholder="%" style="max-width: 80px;">
            <button id="update-sell-percent" class="btn btn-outline-primary" title="Update Sell Trigger %">
                <i class="fas fa-check"></i>
            </button>
        </div>
        <div class="input-group input-group-sm mb-3">
            <label for="trailing-stop-loss-percentage" class="input-group-text">Trailing SL %</label>
            <input type="number" id="trailing-stop-loss-percentage" min="0.1" step="0.1" class="form-control text-center" placeholder="%" style="max-width: 80px;">
            <button id="update-trailing-sl-percent" class="btn btn-outline-primary" title="Update Trailing Stop Loss %">
                <i class="fas fa-check"></i>
            </button>
        </div>
        <h6>Buy on Dip Thresholds</h6>
        <div id="thresholdsList">
            <!-- Threshold items will be populated here by JavaScript -->
        </div>

        <hr>

        <h6>Add New Threshold</h6>
        <div class="input-group mb-3">
            <span class="input-group-text">Threshold (%)</span>
            <input type="number" class="form-control" id="newThresholdValue" placeholder="e.g., 2">
        </div>
        <div class="input-group mb-3">
            <span class="input-group-text">Percentage (%)</span>
            <input type="number" step="0.1" class="form-control" id="newPercentageValue" placeholder="e.g., 20.0">
        </div>
        <button id="addThresholdButton" class="btn btn-primary btn-sm mb-3">Add Threshold</button>


    </div>

    <div class="container-fluid"> <!-- Added pt-4 for spacing below fixed button -->
    <div class="container-xl py-4">
        <!-- Header -->
        <div class="d-flex flex-column flex-lg-row justify-content-between align-items-lg-center gap-3 mb-4 top-nav">
            <div>
                <img src="https://shoonya.com/static/img/shoonya_logo_full.a990668.webp" alt="Shoonya Logo"
                    style="height: 30px;">
            </div>
            <!-- Auto-Pilot Switch -->
            <div class="form-check form-switch fs-5 mx-auto">
                <input class="form-check-input" type="checkbox" role="switch" id="goAutoPilotToggle">
                <label class="form-check-label fw-semibold" for="goAutoPilotToggle">Auto-Pilot</label>
            </div>
            <!-- End Auto-Pilot Switch -->
            <div class="d-flex gap-2 flex-wrap align-items-center">
                <button id="configDrawerButton" class="btn btn-outline-secondary">
                    <i class="fas fa-cog"></i>
                </button>
                <a href="/trades" class="btn btn-primary">
                    <i class="fas fa-history"></i><span> Trade History</span>
                </a>
                <a href="/papertrade" class="btn btn-primary">
                    <i class="fas fa-file-alt"></i><span> Paper Trades</span>
                </a>
                <button id="theme-toggle" class="btn btn-outline-secondary hide-text-mobile">
    <i id="theme-icon" class="fas fa-sun"></i>
</button>
                {% if not login_successful %}
                <button id="relogin-button" class="btn btn-danger hide-text-mobile">
                    <i class="fas fa-sign-in-alt"></i><span> Relogin</span>
                </button>
                {% endif %}
            </div>
        </div>

        <!-- Stat Cards Row -->
        <div class="row g-3">
            <div class="col-6 col-lg-4 col-xl-2">
                 <div class="stat-card" id="cash-amount-card">
                    <div class="stat-label">Available Balance</div>
                    <div id="cash-amount" class="stat-value">
                         <span class="skeleton skeleton-h3"></span>
                    </div>
                    <div class="stat-subtext">Funds available</div>
                </div>
            </div>
             <div class="col-6 col-lg-4 col-xl-2">
                 <div class="stat-card" id="total-active-investment-card">
                    <div class="stat-label">Total Investment</div>
                     <div id="total-active-investment" class="stat-value">
                         <span class="skeleton skeleton-h3"></span>
                     </div>
                    <div class="stat-subtext">Holdings + Positions</div>
                </div>
            </div>
             <div class="col-6 col-lg-4 col-xl-2">
                 <div class="stat-card" id="active-investment-card">
                    <div class="stat-label">Today's Investment</div>
                     <div id="active-investment" class="stat-value">
                         <span class="skeleton skeleton-h3"></span>
                     </div>
                    <div class="stat-subtext">Open positions value</div>
                </div>
            </div>
             <div class="col-6 col-lg-4 col-xl-2">
                 <div class="stat-card" id="unrealized-pnl-card">
                    <div class="stat-label">Open Position P&L</div>
                     <div id="unrealized-pnl" class="stat-value">
                         <span class="skeleton skeleton-h3"></span>
                     </div>
                    <div class="stat-subtext">Day's P/L</div>
                </div>
            </div>
            <div class="col-6 col-lg-4 col-xl-2">
                 <div class="stat-card" id="overall-total-pnl-card">
                    <div class="stat-label">Overall Holdings P&L</div>
                     <div id="overall-total-pnl" class="stat-value">
                         <span class="skeleton skeleton-h3"></span>
                     </div>
                    <div class="stat-subtext">
                        <span>Today:</span>
                        <span id="holdings-day-pnl-amount-subtext"><span class="skeleton skeleton-text-short" style="width: 50px; display: inline-block; height: 0.8rem;"></span></span>
                    </div>
                </div>
            </div>
            <div class="col-6 col-lg-4 col-xl-2">
                 <div class="stat-card" id="overall-total-pnl-percent-card">
                    <div class="stat-label">Overall Holdings P&L %</div>
                     <div id="overall-total-pnl-percent" class="stat-value">
                         <span class="skeleton skeleton-h3"></span>
                     </div>
                     <div class="stat-subtext">
                        <span>Today:</span>
                        <span id="holdings-day-pnl-percent-subtext"><span class="skeleton skeleton-text-short" style="width: 50px; display: inline-block; height: 0.8rem;"></span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Open Positions -->
        <div class="card" id="positions-card">
             <div class="card-header d-flex flex-wrap justify-content-between align-items-center gap-2">
                <h6><i class="fas fa-chart-line me-2 header-icon"></i> Open Positions</h6>
                <div class="refresh-container">
                    <span id="positions-refresh-timer">5</span>s
                    <div id="positions-refresh-dial" class="refresh-dial hidden"></div>
                </div>
            </div>
            <div class="card-body p-0">
                 <div class="common-table-container">
                    <table class="common-table" id="positions-table">
                        <thead>
                            <tr>
                                <th class="col-symbol" data-column="tsym">Symbol</th>
                                <th data-column="quantity">Qty</th>
                                <th data-column="current_price">Current Price</th>
                                <th data-column="avg_price">Avg. Buy</th>
                                <th data-column="invested">Invested</th>
                                <th data-column="unrealized">Unrealized P&L</th>
                                <th>Buy More</th>
                            </tr>
                        </thead>
                        <tbody id="positions-body">
                            <!-- Skeleton Rows -->
                            <tr class="skeleton-row">
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Identified Stocks -->
        <div class="card" id="identified-stocks-card">
            <div class="card-header d-flex flex-wrap justify-content-between align-items-center gap-2">
                <h6><i class="fas fa-bullseye me-2 header-icon"></i> Identified Stocks</h6>
                <div class="form-check form-switch mx-auto" title="Enable auto-buy of identified stocks at 3:15 PM if not already held">
                   <input class="form-check-input" type="checkbox" id="auto-buy-toggle">
                   <label class="form-check-label small" for="auto-buy-toggle">
                       Auto Buy (3:20PM)
                   </label>
                </div>
                <button id="refresh-stocks" class="btn btn-sm btn-outline-primary hide-text-mobile" title="Refresh stocks">
                    <i class="fas fa-sync"></i><span class="d-none d-lg-inline ms-1">Refresh</span>
                </button>
            </div>
            <div class="card-body p-0">
                 <div class="common-table-container">
                    <table class="common-table" id="stock-table">
                        <thead>
                            <tr>
                                <th style="width: 5%;">Buy?</th>
                                <th class="col-company" data-column="name">Company</th>
                                <th class="col-symbol" data-column="symbol">Symbol</th>
                                <th data-column="current_price">Price</th>
                                <th data-column="percentage_change">% Chg</th>
                                <th data-column="date">Date Identified</th>
                            </tr>
                        </thead>
                         <tbody id="stock-table-body">
                             <!-- Skeleton Rows -->
                            <tr class="skeleton-row">
                                <td style="width: 5%;"><span class="skeleton skeleton-checkbox mx-auto"></span></td>
                                <td class="col-company"><span class="skeleton skeleton-text"></span></td>
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                            </tr>
                            <tr class="skeleton-row">
                                <td style="width: 5%;"><span class="skeleton skeleton-checkbox mx-auto"></span></td>
                                <td class="col-company"><span class="skeleton skeleton-text"></span></td>
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                            </tr>
                             <tr class="skeleton-row">
                                <td style="width: 5%;"><span class="skeleton skeleton-checkbox mx-auto"></span></td>
                                <td class="col-company"><span class="skeleton skeleton-text"></span></td>
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="p-3 border-top mt-0" id="identified-stocks-controls">
                     <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center gap-3">
                         <div class="d-flex align-items-center gap-2 flex-grow-1 flex-wrap">
                             <label for="investment-amount" class="form-label text-nowrap mb-0">Amount/Stock:</label>
                             <div class="input-group input-group-sm" style="width: auto;">
                                 <span class="input-group-text">₹</span>
                                 <input type="number" id="investment-amount" min="500" step="100" value="500" class="form-control form-control-sm" style="max-width: 100px;">
                                 <button id="update-amount" class="btn btn-outline-primary hide-text-mobile" title="Save amount preference">
                                     <i class="fas fa-check"></i> <span class="d-none d-lg-inline">Update</span>
                                 </button>
                            </div>
                         </div>
                         <div class="d-flex align-items-center gap-3 justify-content-end flex-wrap">
                             <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="buy-as-paper-trade">
                                <label class="form-check-label" for="buy-as-paper-trade">
                                    Paper Trade
                                </label>
                            </div>
                            <button id="buy-selected" class="btn btn-sm btn-success" disabled>
                                <i class="fas fa-shopping-cart"></i>
                                <span>Buy Selected</span>
                            </button>
                         </div>
                     </div>
                </div>
            </div>
        </div>

        <!-- Current Holdings -->
        <div class="card" id="holdings-card">
            <div class="card-header d-flex flex-wrap justify-content-between align-items-center gap-3 holdings-controls">
                <h6 class="m-0"><i class="fas fa-boxes me-2 header-icon"></i> Current Holdings</h6>

                <!-- Container for percentage inputs -->
                <div class="d-flex flex-column flex-sm-row align-items-center justify-content-center gap-3 percentage-inputs-container">
                    <!-- Sell Trigger % and Trailing SL % moved to Config Drawer -->
                    <div class="form-check form-switch align-self-center ms-sm-2 mt-2 mt-sm-0">
                        <input class="form-check-input" type="checkbox" role="switch" id="toggleBuyOnDip">
                        <label class="form-check-label" for="toggleBuyOnDip" style="white-space: nowrap; display: block; text-align: center;">Buy on Dip</label>
                    </div>
                    <div class="form-check form-switch align-self-center ms-sm-2 mt-2 mt-sm-0">
                        <input class="form-check-input" type="checkbox" role="switch" id="auto-sell-toggle">
                        <label class="form-check-label" for="auto-sell-toggle" style="white-space: nowrap; display: block; text-align: center;">Auto Sell</label>
                    </div>
                </div>

                <div class="refresh-container">
                    <span id="holdings-refresh-timer">3</span>s <!-- Default to 3s for partial updates -->
                    <div id="holdings-refresh-dial" class="refresh-dial hidden"></div>
                </div>
            </div>
            <div class="card-body p-0">
                 <div class="common-table-container">
                    <table class="common-table" id="holdings-table">
                        <thead>
                            <tr>
                                <th style="width: 5%;">Hold?</th>
                                <th class="col-symbol" data-column="symbol">Symbol</th> <!-- data-column: symbol -->
                                <th data-column="quantity">Qty</th> <!-- data-column: quantity -->
                                <th data-column="current_price" class="col-current-price">Current Price</th> <!-- data-column: current_price -->
                                <th data-column="days_pl_amount" class="col-days-pl-amount">Day P/L ₹</th> <!-- data-column: days_pl_amount -->
                                <th data-column="days_pl_percent" class="col-days-pl-percent">Day P/L %</th> <!-- data-column: days_pl_percent -->
                                <th data-column="average_price">Avg. Buy</th> <!-- data-column: average_price -->
                                <th data-column="invested">Invested</th> <!-- data-column: invested -->
                                <th data-column="overall_pl_amount" class="col-overall-pl-amount">Overall P/L ₹</th> <!-- data-column: overall_pl_amount -->
                                <th data-column="overall_pl_percent" class="col-overall-pl-percent">Overall P/L %</th> <!-- data-column: overall_pl_percent -->
                                <th>Instant Sell</th>
                                <th style="width: 15%;">Instant Buy</th>
                            </tr>
                        </thead>
                         <tbody id="holdings-body">
                             <!-- Skeleton Rows -->
                            <tr class="skeleton-row">
                                <td style="width: 5%;"><span class="skeleton skeleton-checkbox mx-auto"></span></td>
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-current-price"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-days-pl-amount"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-days-pl-percent"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-overall-pl-amount"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-overall-pl-percent"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                                <td class="instant-buy-cell">
                                    <div class="skeleton-instant-buy">
                                        <span class="skeleton skeleton-input" style="width:50px; height: calc(1.5em + 0.7rem + 2px);"></span>
                                        <span class="skeleton skeleton-btn" style="margin-left: 4px;"></span>
                                    </div>
                                </td>
                            </tr>
                             <tr class="skeleton-row">
                                <td style="width: 5%;"><span class="skeleton skeleton-checkbox mx-auto"></span></td>
                                <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-current-price"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-days-pl-amount"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-days-pl-percent"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-overall-pl-amount"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="col-overall-pl-percent"><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                                <td class="instant-buy-cell">
                                     <div class="skeleton-instant-buy">
                                        <span class="skeleton skeleton-input" style="width:50px; height: calc(1.5em + 0.7rem + 2px);"></span>
                                        <span class="skeleton skeleton-btn" style="margin-left: 4px;"></span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>

    <!-- Notifications Area -->
    <div id="notifications-container"></div>

    <!-- Notification Drawer -->
    <div id="notification-drawer" class="drawer">
        <div class="drawer-header">
            <h5>Notification History</h5>
            <button type="button" class="btn-close" aria-label="Close" onclick="document.getElementById('notification-drawer').classList.remove('open')"></button>
        </div>
        <div class="drawer-body" id="notification-history">
            <p class="text-muted text-center"><span class="skeleton skeleton-text w-75 mx-auto"></span></p>
        </div>
        <div class="drawer-footer">
             <button id="clear-notifications" class="btn btn-sm btn-outline-danger w-100">Clear History</button>
        </div>
    </div>
    <button id="notification-drawer-toggle" class="drawer-toggle" title="Show Notifications"><i class="fas fa-bell"></i></button>

    <!-- Trailing Logs Drawer -->
    <div id="trailing-logs-drawer" class="drawer">
         <div class="drawer-header">
            <h5>Trailing Stop Logs</h5>
             <button type="button" class="btn-close" aria-label="Close" onclick="document.getElementById('trailing-logs-drawer').classList.remove('open')"></button>
        </div>
        <div class="drawer-body" id="trailing-logs-content">
             <p class="text-muted text-center"><span class="skeleton skeleton-text w-75 mx-auto"></span></p>
        </div>
         <div class="drawer-footer">
            <button id="clear-trailing-logs" class="btn btn-sm btn-outline-danger w-100">Clear Logs</button>
        </div>
    </div>


    <button id="trailing-logs-toggle" class="drawer-toggle" title="Show Trailing Logs"><i class="fas fa-clipboard-list"></i></button>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.5/dist/sweetalert2.all.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const configDrawer = document.getElementById('configDrawer');
            const configDrawerButton = document.getElementById('configDrawerButton');
            const thresholdsList = document.getElementById('thresholdsList');
            const newThresholdValueInput = document.getElementById('newThresholdValue');
            const newPercentageValueInput = document.getElementById('newPercentageValue');
            const addThresholdButton = document.getElementById('addThresholdButton');

            let currentBuyOnDipConfig = [];

            // Function to fetch config when drawer is opened
            // Note: The actual drawer toggle is handled by the event listener at the bottom of the file
            document.addEventListener('drawerOpened', function() {
                fetchBuyOnDipConfig();
            });

            // Function to render threshold items
            function renderThresholds() {
                thresholdsList.innerHTML = ''; // Clear existing items
                if (currentBuyOnDipConfig.length === 0) {
                    thresholdsList.innerHTML = '<p class="text-muted">No thresholds configured.</p>';
                    return;
                }
                currentBuyOnDipConfig.forEach((item, index) => {
                    const div = document.createElement('div');
                    div.classList.add('threshold-item');
                    div.innerHTML = `
                        <div class="threshold-values">
                            Threshold: <strong>${item.threshold}%</strong>, Percentage: <strong>${item.percentage}%</strong>
                        </div>
                        <button class="btn btn-danger btn-sm remove-threshold-btn" data-index="${index}"><i class="fas fa-trash-alt"></i></button>
                    `;
                    thresholdsList.appendChild(div);
                });

                // Add event listeners to new remove buttons
            document.querySelectorAll('.remove-threshold-btn').forEach(button => {
                button.addEventListener('click', async function () {
                    const indexToRemove = parseInt(this.getAttribute('data-index'));
                    const removedItem = currentBuyOnDipConfig[indexToRemove];
                    currentBuyOnDipConfig.splice(indexToRemove, 1);
                    renderThresholds(); // Re-render the list
                    
                    // Save configuration immediately
                    try {
                        const response = await fetch('/api/config/buy_on_dip', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ buy_on_dip_config: currentBuyOnDipConfig }),
                        });
                        if (!response.ok) {
                            const errorData = await response.json();
                            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                        }
                        window.showToast(`Threshold ${removedItem.threshold}% removed successfully`, 'success');
                    } catch (error) {
                        console.error('Error saving configuration:', error);
                        window.showToast(`Error removing threshold: ${error.message}`, 'error');
                        // Revert the change on error
                        currentBuyOnDipConfig.splice(indexToRemove, 0, removedItem);
                        currentBuyOnDipConfig.sort((a, b) => a.threshold - b.threshold);
                        renderThresholds();
                    }
                });
            });
            }

            // Function to fetch buy_on_dip_config
            async function fetchBuyOnDipConfig() {
                try {
                    const response = await fetch('/api/config/buy_on_dip');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    currentBuyOnDipConfig = data.buy_on_dip_config || [];
                    renderThresholds();
                } catch (error) {
                    console.error('Error fetching buy_on_dip_config:', error);
                    Swal.fire('Error', 'Could not load configuration.', 'error');
                    currentBuyOnDipConfig = []; // Reset on error
                    renderThresholds();
                }
            }

            // Event listener for adding a new threshold
            addThresholdButton.addEventListener('click', async function () {
                const threshold = parseFloat(newThresholdValueInput.value);
                const percentage = parseFloat(newPercentageValueInput.value);

                if (isNaN(threshold) || isNaN(percentage) || threshold <= 0 || percentage <= 0) {
                    window.showToast('Please enter valid positive numbers for threshold and percentage', 'warning');
                    return;
                }
                
                // Check for duplicate thresholds
                if (currentBuyOnDipConfig.some(item => item.threshold === threshold)) {
                    window.showToast(`A threshold of ${threshold}% already exists`, 'warning');
                    return;
                }

                const newItem = { threshold, percentage };
                currentBuyOnDipConfig.push(newItem);
                currentBuyOnDipConfig.sort((a, b) => a.threshold - b.threshold); // Keep sorted
                renderThresholds();
                newThresholdValueInput.value = '';
                newPercentageValueInput.value = '';
                
                // Save configuration immediately
                try {
                    const response = await fetch('/api/config/buy_on_dip', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ buy_on_dip_config: currentBuyOnDipConfig }),
                    });
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                    }
                    window.showToast(`Threshold ${threshold}% added successfully`, 'success');
                } catch (error) {
                    console.error('Error saving configuration:', error);
                    window.showToast(`Error adding threshold: ${error.message}`, 'error');
                    // Revert the change on error
                    const index = currentBuyOnDipConfig.findIndex(item => item.threshold === threshold && item.percentage === percentage);
                    if (index > -1) {
                        currentBuyOnDipConfig.splice(index, 1);
                        renderThresholds();
                    }
                }
            });



            // Initial fetch if needed or if drawer starts open (though it doesn't by default)
            // fetchBuyOnDipConfig(); 
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- Global State ---
            window.stocksData = []; // Identified Stocks
            window.positionsData = []; // Open Positions
            window.holdingsData = []; // Current Holdings (stores full holding objects)
            window.dontSellData = { symbols: [], sell_percentage: 2.0, trailing_stop_loss_percentage: 0.5, auto_sell_enabled: false }; // User Hold Preferences + % Config
            window.activeSymbols = []; // Symbols actively being trailed (full trading symbols)
            window.configData = { investment_amount: 500, auto_buy_enabled: false }; // General config like investment amount, auto-buy status
            window.buyOnDipConfig = { enabled: false, config: [] }; // For Buy on Dip feature - Updated structure
            window.buyOnDipState = {}; // Stores the buy on dip state including triggered thresholds
            let lastCashAmount = null;
            let lastHoldingsPnl = null; // Track last P&L value for title update
            let isInitialHoldingsLoad = true; // Flag for first full holdings load

            // --- Theme ---
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = document.getElementById('theme-icon');
            let isDarkMode = localStorage.getItem('theme') === 'dark' ||
                             (localStorage.getItem('theme') === null && window.matchMedia('(prefers-color-scheme: dark)').matches);

            const updateTheme = (dark) => {
                isDarkMode = dark;
                const newTheme = isDarkMode ? 'dark' : 'light';
                document.documentElement.setAttribute('data-bs-theme', newTheme);
                themeIcon.className = `fas fa-${isDarkMode ? 'moon' : 'sun'}`;
                const span = themeToggle.querySelector('span.d-none.d-lg-inline');
                if(span) span.textContent = isDarkMode ? 'Dark' : 'Light';
                localStorage.setItem('theme', newTheme);
                 const swalPopups = document.querySelectorAll('.swal2-popup');
                 swalPopups.forEach(popup => {
                    popup.setAttribute('data-bs-theme', newTheme);
                 });
                 Swal.update({ customClass: { popup: `swal2-${newTheme}` } });
            };
            themeToggle.addEventListener('click', () => updateTheme(!isDarkMode));
            updateTheme(isDarkMode);

            // --- Drawers ---
             const notificationDrawer = document.getElementById('notification-drawer');
             const notificationDrawerToggle = document.getElementById('notification-drawer-toggle');
             const trailingLogsDrawer = document.getElementById('trailing-logs-drawer');
             const trailingLogsToggle = document.getElementById('trailing-logs-toggle');

             notificationDrawerToggle.addEventListener('click', () => notificationDrawer.classList.toggle('open'));
             trailingLogsToggle.addEventListener('click', () => trailingLogsDrawer.classList.toggle('open'));

             // Close drawer if clicking outside
             document.addEventListener('click', (event) => {
                if (notificationDrawer.classList.contains('open') && !notificationDrawer.contains(event.target) && !notificationDrawerToggle.contains(event.target)) {
                    notificationDrawer.classList.remove('open');
                }
                 if (trailingLogsDrawer.classList.contains('open') && !trailingLogsDrawer.contains(event.target) && !trailingLogsToggle.contains(event.target)) {
                    trailingLogsDrawer.classList.remove('open');
                }
             });

             async function handleFetchError(response, context = "operation") {
                let errorMsg = `Failed to ${context}`;
                try {
                    const contentType = response.headers.get("content-type");
                    if (contentType && !contentType.includes("application/json")) {
                         errorMsg = `Server returned an unexpected response type (${contentType || 'unknown'}) for ${context}. Status: ${response.status} ${response.statusText}`;
                        return new Error(errorMsg);
                    }

                    const errData = await response.json();
                    errorMsg = errData.error || errData.message || `Failed to ${context} - Server Status: ${response.statusText}`;
                } catch (parseError) {
                     if (parseError instanceof SyntaxError && parseError.message.includes("Unexpected token")) {
                         errorMsg = `Server returned an invalid response (possibly HTML error page) for ${context}. Status: ${response.status} ${response.statusText}`;
                     } else {
                         errorMsg = `Failed to parse error response for ${context}. Status: ${response.status} ${response.statusText}. Parse Error: ${parseError.message}`;
                     }
                     console.error(`Parse Error during error handling for ${context}:`, parseError);
                }
                return new Error(errorMsg);
            }


            // --- Initial Data Loading ---
            async function initializeDashboard() {
                renderTableSkeletons('positions-body', 7, 2);
                renderTableSkeletons('stock-table-body', 6, 3);
                renderTableSkeletons('holdings-body', 12, 2);
                document.title = "Finvasia | Loading...";
                isInitialHoldingsLoad = true;


                // Fetch all configurations concurrently
                try {
                    await Promise.all([
                        fetchConfigData(),
                        fetchDontSellConfig(),
                        fetchBuyOnDipConfig(), // Fetch Buy on Dip configuration
                        fetch('/api/buy_on_dip_state').then(res => res.json()).then(data => { window.buyOnDipState = data; console.log('Buy on Dip state loaded:', data); }).catch(err => { console.error('Error fetching buy_on_dip_state:', err); window.buyOnDipState = {}; }) // Fetch Buy on Dip State
                    ]);
                    // Update UI elements after fetching config
                    loadConfiguredPercentages();
                    loadInvestmentAmount();
                    loadAutoBuyStatus();
                    loadAutoSellStatus(); // Added for auto-sell toggle
                } catch (error) {
                     console.error("Error fetching initial configurations:", error.message || error);
                     showToast(`Error loading initial configuration: ${error.message || 'Unknown error'}`, "error");
                     loadConfiguredPercentages();
                     loadInvestmentAmount();
                     loadAutoBuyStatus();
                     loadAutoSellStatus(); // Added for auto-sell toggle
                 }


                updateCashDisplay();
                fetchStocks(); // Identified stocks
                updatePositionsDisplay(); // Positions
                updateHoldingsDisplay(); // Holdings (full initial load)
                updateNotificationDrawer(true); // Load history (pass true for initial load)
                updateTrailingLogs(true); // Trailing logs (pass true for initial load)
            }

             // --- Configuration Fetching ---
            async function fetchConfigData() {
                try {
                    const amountResponse = await fetch('/api/investment_amount');
                    const autoBuyResp = await fetch('/api/auto_buy_config');

                    if (!amountResponse.ok) throw await handleFetchError(amountResponse, "fetch investment amount");
                    if (!autoBuyResp.ok) throw await handleFetchError(autoBuyResp, "fetch auto-buy config");

                    const amountData = await amountResponse.json();
                    const autoBuyData = await autoBuyResp.json();
                    window.configData.investment_amount = amountData.amount || 500;
                    window.configData.auto_buy_enabled = autoBuyData.enabled || false;
                    console.log("Fetched config data:", window.configData);

                } catch (error) {
                     console.error('Error fetching base config data:', error.message || error);
                     throw error;
                }
            }

             async function fetchDontSellConfig() {
                 try {
                    const response = await fetch('/api/dont_sell');
                    if (!response.ok) throw await handleFetchError(response, "fetch don't sell config");

                    const data = await response.json();
                    window.dontSellData.symbols = data.symbols || [];
                    window.dontSellData.sell_percentage = data.sell_percentage || 2.0;
                    window.dontSellData.trailing_stop_loss_percentage = data.trailing_stop_loss_percentage || 0.5;
                    window.dontSellData.auto_sell_enabled = data.auto_sell_enabled || false; // Fetch auto_sell_enabled
                    console.log("Fetched Don't Sell config:", window.dontSellData);

                 } catch (error) {
                     console.error('Error fetching dont_sell config:', error.message || error);
                     throw error;
                 }
             }

            async function fetchBuyOnDipConfig() {
                try {
                    const response = await fetch('/api/buy_on_dip_config');
                    if (!response.ok) throw await handleFetchError(response, "fetch Buy on Dip config");
                    const data = await response.json();
                    window.buyOnDipConfig.enabled = data.buy_on_dip_enabled || false;
                    window.buyOnDipConfig.config = data.buy_on_dip_config || []; // Updated to new structure


                    const toggleBuyOnDipSwitch = document.getElementById('toggleBuyOnDip');
                    if (toggleBuyOnDipSwitch) {
                        toggleBuyOnDipSwitch.checked = window.buyOnDipConfig.enabled;
                    }
                    console.log("Fetched Buy on Dip config:", window.buyOnDipConfig);
                } catch (error) {
                    console.error('Error fetching Buy on Dip config:', error.message || error);
                    showToast(`Error loading Buy on Dip status: ${error.message || 'Check console.'}`, 'error');
                    const toggleBuyOnDipSwitch = document.getElementById('toggleBuyOnDip');
                    if (toggleBuyOnDipSwitch) toggleBuyOnDipSwitch.checked = false; 
                }
            }

            async function updateBuyOnDipConfig(enabled) {
                const toggleSwitch = document.getElementById('toggleBuyOnDip');
                const originalState = toggleSwitch ? toggleSwitch.checked : !enabled;

                try {
                    const response = await fetch('/api/buy_on_dip_config', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ enabled: enabled }),
                    });
                    if (!response.ok) throw await handleFetchError(response, "update Buy on Dip config");
                    const data = await response.json();

                    if (data.status === 'success' && data.config) {
                        window.buyOnDipConfig.enabled = data.config.buy_on_dip_enabled;
                        window.buyOnDipConfig.config = data.config.buy_on_dip_config || []; // Update config on successful POST
                        if (toggleSwitch) {
                            toggleSwitch.checked = window.buyOnDipConfig.enabled;
                        }
                        showToast(`Buy on Dip feature ${window.buyOnDipConfig.enabled ? 'enabled' : 'disabled'}.`, 'success');
                        console.log('Buy on Dip config updated:', data.config);
                    } else {
                        throw new Error(data.message || 'Failed to update Buy on Dip status.');
                    }
                } catch (error) {
                    console.error('Error updating Buy on Dip config:', error.message || error);
                    showToast(`Error updating Buy on Dip: ${error.message || 'Check console.'}`, 'error');
                    if (toggleSwitch) {
                        toggleSwitch.checked = originalState; 
                        const goAutoPilotActive = document.getElementById('goAutoPilotToggle')?.checked || false;
                        toggleSwitch.disabled = goAutoPilotActive;
                    }
                }
            }

             // --- UI Updates for Config ---
             function loadConfiguredPercentages() {
                 const sellPercentInput = document.getElementById('sell-percentage');
                 const trailingSLPercentInput = document.getElementById('trailing-stop-loss-percentage');
                 if (sellPercentInput) sellPercentInput.value = window.dontSellData.sell_percentage || 2.0;
                 if (trailingSLPercentInput) trailingSLPercentInput.value = window.dontSellData.trailing_stop_loss_percentage || 0.5;
             }

             function loadInvestmentAmount() {
                 const investmentAmountInput = document.getElementById('investment-amount');
                 if (investmentAmountInput) investmentAmountInput.value = window.configData.investment_amount || 500;
             }

            function loadAutoBuyStatus() {
                const autoBuyToggle = document.getElementById('auto-buy-toggle');
                if (autoBuyToggle) autoBuyToggle.checked = window.configData.auto_buy_enabled || false;
            }

            function loadAutoSellStatus() {
                const autoSellToggle = document.getElementById('auto-sell-toggle');
                if (autoSellToggle) autoSellToggle.checked = window.dontSellData.auto_sell_enabled || false;
            }

            // --- Notifications ---
            const notificationsContainer = document.getElementById('notifications-container');
            const notificationHistory = document.getElementById('notification-history');

            window.showToast = function(message, type = 'success', duration = 5000) {
                const iconClass = type === 'success' ? 'fa-check-circle text-success' :
                                 type === 'error' ? 'fa-times-circle text-danger' :
                                 type === 'info' ? 'fa-info-circle text-info' :
                                 type === 'warning' ? 'fa-exclamation-triangle text-warning' :
                                 'fa-bell';
                const toast = document.createElement('div');
                toast.className = `order-result ${type}`;
                toast.innerHTML = `
                    <button type="button" class="notification-close" aria-label="Close">&times;</button>
                    <i class="fas ${iconClass}"></i> ${message}
                `;
                notificationsContainer.appendChild(toast);
                const timestamp = new Date().toISOString();

                const timer = setTimeout(() => {
                     toast.style.animation = 'toast-out 0.4s ease-out forwards';
                     toast.addEventListener('animationend', () => {
                         toast.remove();
                         storeNotification(type, message, timestamp);
                     }, { once: true });
                }, duration);

                toast.querySelector('.notification-close').addEventListener('click', (e) => {
                    e.stopPropagation();
                    clearTimeout(timer);
                    toast.style.animation = 'toast-out 0.4s ease-out forwards';
                     toast.addEventListener('animationend', () => {
                         toast.remove();
                         storeNotification(type, message, timestamp);
                     }, { once: true });
                });
            }

            function storeNotification(type, content, timestamp) {
                let notifications = JSON.parse(localStorage.getItem('notifications') || '[]');
                if (notifications.length > 0 && notifications[notifications.length - 1].content === content) {
                     return;
                }
                if (notifications.length >= 50) notifications.shift();
                notifications.push({ type, content, timestamp });
                localStorage.setItem('notifications', JSON.stringify(notifications));
                updateNotificationDrawer();
            }

             function updateNotificationDrawer(initialLoad = false) {
                 const historyContainer = document.getElementById('notification-history');
                 if (!historyContainer) return;

                if (initialLoad) {
                    historyContainer.innerHTML = '<p class="text-muted text-center"><span class="skeleton skeleton-text w-75 mx-auto"></span></p>';
                }
                let notifications = JSON.parse(localStorage.getItem('notifications') || '[]');
                if (notifications.length === 0) {
                    historyContainer.innerHTML = '<p class="text-muted text-center mt-3">No notifications yet.</p>';
                    return;
                }

                const groupedNotifications = {};
                notifications.forEach(n => {
                    const dateStr = new Date(n.timestamp).toISOString().split('T')[0];
                    groupedNotifications[dateStr] = groupedNotifications[dateStr] || [];
                    groupedNotifications[dateStr].push(n);
                });

                 historyContainer.innerHTML = Object.keys(groupedNotifications)
                    .sort((a, b) => b.localeCompare(a))
                    .map((dateStr, dateIndex) => {
                        const displayDate = new Date(dateStr + 'T00:00:00Z').toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
                        return `
                        <div class="mb-3">
                            <div class="notification-date content-fade-in" style="animation-delay: ${dateIndex * 0.1}s">${displayDate}</div>
                            ${groupedNotifications[dateStr]
                                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                                .map((n, itemIndex) => {
                                    const icon = n.type === 'success' ? 'fa-check-circle text-success' :
                                                 n.type === 'error' ? 'fa-times-circle text-danger' :
                                                 n.type === 'info' ? 'fa-info-circle text-info' :
                                                 n.type === 'warning' ? 'fa-exclamation-triangle text-warning' :
                                                 'fa-bell';
                                    const time = new Date(n.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
                                    return `
                                    <div class="drawer-notification" style="animation-delay: ${dateIndex * 0.1 + (itemIndex + 1) * 0.05}s">
                                        <div><i class="fas ${icon} me-2"></i> ${n.content}</div>
                                        <div class="notification-time">${time}</div>
                                    </div>`;
                                }).join('')}
                        </div>
                        `;
                    }).join('');
            }


            document.getElementById('clear-notifications').addEventListener('click', () => {
                localStorage.removeItem('notifications');
                showToast('Notification history cleared', 'info');
                updateNotificationDrawer();
            });

            // --- Trailing Logs ---
             const logsContent = document.getElementById('trailing-logs-content');

             function updateTrailingLogs(initialLoad = false) {
                if (initialLoad && logsContent) {
                    logsContent.innerHTML = '<p class="text-muted text-center"><span class="skeleton skeleton-text w-75 mx-auto"></span></p>';
                }
                fetch('/api/trailing_logs')
                    .then(async response => {
                        if (!response.ok) throw await handleFetchError(response, "fetch trailing logs");
                        return response.json();
                    })
                    .then(logs => {
                        if (!logsContent) return;
                        if (!Array.isArray(logs)) {
                             logsContent.innerHTML = '<p class="text-danger text-center mt-3">Error: Invalid log data received.</p>';
                             return;
                        }
                        if (logs.length === 0) {
                            logsContent.innerHTML = '<p class="text-muted text-center mt-3">No trailing logs recorded yet.</p>';
                            return;
                        }
                         logsContent.innerHTML = logs
                            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                            .map((log, index) => {
                                let displayMessage = log.message;
                                if (log.message === "Market is closed..!!") {
                                    displayMessage = `<div class="market-closed-msg"><i class="fas fa-door-closed me-1"></i>Market Closed</div>`;
                                } else if (log.message.startsWith("Login required")) {
                                    displayMessage = `<div class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>${log.message}</div>`;
                                } else if (log.message.toLowerCase().includes("error")) {
                                     displayMessage = `<div class="text-danger"><i class="fas fa-times-circle me-1"></i>${log.message}</div>`;
                                } else if (log.message.toLowerCase().includes("success")) {
                                     displayMessage = `<div class="text-success"><i class="fas fa-check-circle me-1"></i>${log.message}</div>`;
                                }
                                return `
                                <div class="trailing-log-entry" style="animation-delay: ${index * 0.05}s">
                                    <div class="trailing-log-timestamp">${new Date(log.timestamp).toLocaleString([], { dateStyle: 'short', timeStyle: 'medium' })}</div>
                                    <div>${displayMessage}</div>
                                </div>`;
                        }).join('');
                    })
                    .catch(error => {
                         if (logsContent) logsContent.innerHTML = '<p class="text-danger text-center mt-3">Error loading trailing logs.</p>';
                         console.error('Error fetching trailing logs:', error.message || error);
                         if (!error.message?.includes("Login required") && !error.message?.includes("invalid response")) {
                            showToast(`Could not fetch trailing logs: ${error.message || 'Unknown error'}`, 'error', 3000);
                         }
                     });
            }

            document.getElementById('clear-trailing-logs').addEventListener('click', () => {
                fetch('/api/clear_trailing_logs', { method: 'POST' })
                    .then(async response => {
                        if (!response.ok) throw await handleFetchError(response, "clear trailing logs");
                        return response.json();
                    })
                    .then(data => {
                        if (data.status === 'success') {
                            updateTrailingLogs();
                            showToast('Trailing logs cleared', 'info');
                        } else {
                            throw new Error(data.message || "Failed to clear logs");
                        }
                    })
                    .catch(error => showToast(`Error clearing logs: ${error.message || error}`, 'error'));
            });

             // --- Helper: Update Stat Element & Remove Skeleton ---
            function updateStatElement(id, value, isPnl = false, addRupee = true) {
                const element = document.getElementById(id);
                // Target the card if it exists and is a main stat, not a subtext element
                const card = !id.includes('subtext') ? document.getElementById(`${id}-card`) : null;

                if (element) {
                    const skeleton = element.querySelector('.skeleton');
                    if (skeleton) skeleton.remove();

                    let displayValue = value;
                    // Add Rupee sign only if it's not '---' and not already present and not a percentage
                    if (addRupee && typeof value === 'string' && value !== '---' && !value.startsWith('₹') && !value.endsWith('%')) {
                         displayValue = `₹${value}`;
                    }
                     // Remove Rupee sign if it's a percentage or '---'
                     if (!addRupee && typeof value === 'string') {
                        if (value.endsWith('%') || value === '---') {
                             // No change needed for % or '---'
                        } else if (value.startsWith('₹')) {
                             displayValue = value.substring(1); // Remove rupee if addRupee is false
                        }
                     }

                    element.textContent = displayValue;
                    element.classList.add('content-fade-in');

                    if (isPnl) {
                        // Use a more robust way to extract number, handling potential '---'
                        let numericValue = 0;
                        if (typeof value === 'string' && value !== '---') {
                           numericValue = parseFloat(value.replace(/[₹%,]/g, '')) || 0;
                        } else if (typeof value === 'number') {
                            numericValue = value;
                        }

                        element.classList.remove('profit-positive', 'profit-negative', 'text-success', 'text-danger');
                        if (value !== '---') { // Only add class if not '---'
                           element.classList.add(numericValue >= 0 ? 'profit-positive' : 'profit-negative');
                        }
                    }
                }
                 if (card && isInitialHoldingsLoad) { // Only update card skeletons if it's a main stat update AND initial load
                     card.querySelectorAll('.stat-label .skeleton, .stat-subtext .skeleton:not([id^="holdings-day-pnl"])').forEach(sk => sk.remove()); // Avoid removing our specific subtext skeletons here
                     card.classList.add('content-fade-in');
                 }
            }

            function updateCashDisplay() {
                fetch('/api/limits')
                    .then(async response => {
                         if (!response.ok) {
                             throw await handleFetchError(response, "fetch balance");
                         }
                         return response.json();
                     })
                    .then(data => {
                         if (data.error && data.error.includes("Login required")) {
                              throw new Error("Login required");
                         }
                         if (data.error) {
                              throw new Error(data.error);
                         }
                         const cash = data.cash;
                         if (cash !== null && cash !== undefined) {
                            const formattedCash = parseFloat(cash).toFixed(2);
                             updateStatElement('cash-amount', formattedCash);
                            lastCashAmount = formattedCash;
                         } else { throw new Error("Invalid cash amount in response"); }
                    })
                    .catch(error => {
                         console.error(`Balance Fetch Error: ${error.message || error}`);
                         updateStatElement('cash-amount', '---', false, false);
                         if (error.message === "Login required" && !document.getElementById('relogin-button')) {
                            showToast('Session likely expired. Please relogin.', 'error');
                         } else if (error.message !== "Login required") {
                             showToast(`Could not fetch balance: ${error.message.substring(0, 70)}`, 'error', 3000);
                         }
                     });
            }

             function updateTotalActiveInvestment() {
                const totalPositionsInvestment = window.positionsData.reduce((sum, pos) => sum + (pos.invested || 0), 0);
                const totalHoldingsInvestment = window.holdingsData.reduce((sum, hld) => sum + (hld.invested || 0), 0);
                const total = totalPositionsInvestment + totalHoldingsInvestment;
                 updateStatElement('total-active-investment', total.toFixed(2));
            }

             function updateOverallPLStats(holdingsToCalculate = window.holdingsData) {
                let totalPLAmount = 0;
                let totalInvestment = 0;
                let totalDaysPlAmount = 0;
                let totalInitialInvestmentDay = 0;

                holdingsToCalculate.forEach(h => {
                    totalPLAmount += h.overall_pl_amount || 0;
                    totalInvestment += h.invested || 0;
                    totalDaysPlAmount += h.days_pl_amount || 0;
                    const lastDayPrice = typeof h.last_day_price === 'number' ? h.last_day_price : (h.average_price || 0);
                    totalInitialInvestmentDay += lastDayPrice * (h.quantity || 0);
                });

                const totalPLPercent = totalInvestment > 0 ? (totalPLAmount / totalInvestment) * 100 : 0;
                const daysOverallPlPercent = totalInitialInvestmentDay > 0 ? (totalDaysPlAmount / totalInitialInvestmentDay) * 100 : 0;

                 updateStatElement('overall-total-pnl', totalPLAmount.toFixed(2), true);
                 updateStatElement('overall-total-pnl-percent', `${totalPLPercent.toFixed(2)}%`, true, false);
                 updateStatElement('holdings-day-pnl-amount-subtext', totalDaysPlAmount.toFixed(2), true, true);
                 updateStatElement('holdings-day-pnl-percent-subtext', `${daysOverallPlPercent.toFixed(2)}%`, true, false);

                 // Update page title based on Holdings Overall P/L
                 const currentPnlString = totalPLAmount.toFixed(2);
                 if (lastHoldingsPnl === null || Math.abs(parseFloat(lastHoldingsPnl) - totalPLAmount) > 0.01 || document.title.includes("Loading") || document.title.includes("Error")) {
                    lastHoldingsPnl = currentPnlString;
                    const pnlSign = totalPLAmount >= 0 ? '+' : '-';
                    const formattedPnl = `${pnlSign}₹${Math.abs(totalPLAmount).toFixed(2)}`;
                    const pnlPercentSign = totalPLPercent >= 0 ? '+' : '-';
                    const formattedPercent = `${pnlPercentSign}${Math.abs(totalPLPercent).toFixed(2)}%`;
                    document.title = `${formattedPnl} (${formattedPercent}) : P&L`;
                 }
            }


            let positionsRefreshInterval;
            const positionsRefreshTimer = document.getElementById('positions-refresh-timer');
            const positionsRefreshDial = document.getElementById('positions-refresh-dial');
            let positionsTimeRemaining = 5;

            function startPositionsRefreshTimer() {
                clearInterval(positionsRefreshInterval);
                if (!positionsRefreshTimer || !positionsRefreshDial) return;
                positionsTimeRemaining = 5;
                positionsRefreshDial.classList.remove('hidden');
                positionsRefreshTimer.textContent = positionsTimeRemaining;

                positionsRefreshInterval = setInterval(() => {
                    positionsTimeRemaining--;
                    if (positionsRefreshTimer) positionsRefreshTimer.textContent = positionsTimeRemaining;
                    if (positionsTimeRemaining <= 0) {
                        clearInterval(positionsRefreshInterval);
                        if (positionsRefreshDial) positionsRefreshDial.classList.add('hidden');
                        updatePositionsDisplay();
                    }
                }, 1000);
            }

             function updatePositionsDisplay() {
                 fetch('/api/positions')
                     .then(async response => {
                         if (!response.ok) {
                             throw await handleFetchError(response, "fetch positions");
                         }
                         return response.json();
                     })
                     .then(data => {
                        if (data.error) throw new Error(data.error);

                        const positions = data.positions || [];
                        positions.sort((a, b) => (b.invested || 0) - (a.invested || 0));
                        window.positionsData = positions;

                        renderPositionsTable(positions);

                        updateStatElement('active-investment', (data.total_invested || 0).toFixed(2));
                        updateStatElement('unrealized-pnl', (data.total_unrealized || 0).toFixed(2), true);

                        updateTotalActiveInvestment();
                     })
                     .catch(error => {
                         console.error('Error fetching positions:', error.message || error);
                         renderPositionsTable([]);
                         updateStatElement('active-investment', '---', false, false);
                         updateStatElement('unrealized-pnl', '---', true, false);
                         if (!error.message?.includes("Login required")) {
                             showToast(`Could not fetch positions: ${error.message || 'Unknown Error'}`, 'error');
                         }
                     })
                     .finally(() => {
                         startPositionsRefreshTimer();
                     });
             }

             function renderPositionsTable(positions) {
                const tbody = document.getElementById('positions-body');
                if (!tbody) return;
                if (positions.length === 0) {
                    tbody.innerHTML = `<tr><td colspan="7" class="text-center text-muted py-3">No open positions.</td></tr>`;
                    return;
                }
                 const currentSortHeader = document.querySelector('#positions-table th.asc, #positions-table th.desc');
                 const sortColumn = currentSortHeader?.dataset.column;
                 const sortAsc = currentSortHeader?.classList.contains('asc');

                 tbody.innerHTML = positions.map((pos, index) => {
                     const tradingSymbol = pos.tsym || 'N/A';
                     const baseSymbol = tradingSymbol.split('-')[0];
                     const avgPrice = typeof pos.avg_price === 'number' ? pos.avg_price : 0;
                     const quantity = typeof pos.quantity === 'number' ? pos.quantity : 0;
                     const currentPrice = typeof pos.current_price === 'number' ? pos.current_price : avgPrice;
                     const invested = typeof pos.invested === 'number' ? pos.invested : avgPrice * quantity;
                     const unrealizedPnl = typeof pos.unrealized === 'number' ? pos.unrealized : (currentPrice - avgPrice) * quantity;

                     const pnlClass = unrealizedPnl >= 0 ? 'profit-positive' : 'profit-negative';
                     const showBuyButton = quantity > 0;

                     return `
                    <tr class="stock-row content-fade-in" style="animation-delay: ${index * 0.05}s">
                        <td class="col-symbol-link">
                            <a href="https://www.tradingview.com/chart/?symbol=NSE%3A${baseSymbol}" target="_blank" class="stock-badge">${tradingSymbol}</a>
                        </td>
                        <td>${quantity}</td>
                        <td>₹${currentPrice.toFixed(2)}</td>
                        <td>₹${avgPrice.toFixed(2)}</td>
                        <td>₹${invested.toFixed(2)}</td>
                        <td><span class="${pnlClass}">₹${unrealizedPnl.toFixed(2)}</span></td>
                        <td class="actions-cell">
                            ${showBuyButton ? `
                                <button class="btn btn-sm btn-success buy-position"
                                    data-symbol="${tradingSymbol}"
                                    data-name="${baseSymbol}"
                                    data-price="${currentPrice}">
                                    <i class="fas fa-plus"></i>
                                </button>` : ''}
                        </td>
                    </tr>`;
                 }).join('');

                 if (sortColumn) {
                     const headerToMark = document.querySelector(`#positions-table th[data-column="${sortColumn}"]`);
                     if (headerToMark) {
                         headerToMark.classList.add(sortAsc ? 'asc' : 'desc');
                     }
                 }

                tbody.querySelectorAll('.buy-position').forEach(btn => {
                    btn.addEventListener('click', handleBuyPositionClick);
                });
             }

             async function handleBuyPositionClick(event) { // Made async
                 const btn = event.currentTarget;
                 const tradingSymbol = btn.dataset.symbol;
                 const name = btn.dataset.name;
                 const price = parseFloat(btn.dataset.price); // Kept for context
                 const quantity = 1; // Position buy button always buys 1 share
                 const originalBtnHTML = btn.innerHTML;

                 if (!tradingSymbol || tradingSymbol === 'N/A') {
                     showToast('Error: Missing symbol data.', 'error'); return;
                 }

                 setButtonLoading(btn, true);

                 try {
                     const response = await fetch('/buy_stocks', {
                        method: 'POST', headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            stocks: [{ symbol: name, name: name }], // API expects 'symbol' as base, 'name' as base
                            quantity: quantity
                        })
                     });

                     if (!response.ok) throw await handleFetchError(response, `buy ${name}`);
                     const data = await response.json();

                     if (data.error) throw new Error(data.error);

                     const resultForStock = data.results?.find(r => r.symbol === tradingSymbol);
                     if (resultForStock && !resultForStock.error && resultForStock.order_response?.stat === 'Ok') {
                         showToast(`Successfully bought ${quantity} share(s) of ${tradingSymbol}`, 'success');
                         updateCashDisplay(); // Assuming synchronous

                         // Ensure 'Don't Sell' list, holdings, and positions are up-to-date in order
                         await fetchDontSellConfig();    // Fetch latest "Don't Sell" list
                         await updateHoldingsDisplay();  // Fetches holdings & re-renders holdings table (uses fresh dontSellData)
                         await updatePositionsDisplay(); // Refresh positions table (await if async)
                     } else {
                         const errorMsg = resultForStock?.error || data.message || 'Buy operation failed for '+tradingSymbol;
                         throw new Error(errorMsg);
                     }
                 } catch (error) {
                     showToast(`Error buying ${name}: ${error.message || error}`, 'error');
                 } finally {
                     setButtonLoading(btn, false, originalBtnHTML);
                 }
             }

             const refreshStocksButton = document.getElementById('refresh-stocks');
             if (refreshStocksButton) refreshStocksButton.addEventListener('click', () => fetchStocks());

             function fetchStocks() {
                 if (!refreshStocksButton) return;
                setButtonLoading(refreshStocksButton, true);
                renderTableSkeletons('stock-table-body', 6, 2);

                return fetch('/api/stocks')
                     .then(async response => {
                         if (!response.ok) throw await handleFetchError(response, "fetch stocks");
                         return response.json();
                     })
                    .then(stocks => {
                         if (!Array.isArray(stocks)) throw new Error("Invalid stock data received");

                         stocks.sort((a, b) => (a.symbol || '').localeCompare(b.symbol || ''));
                        window.stocksData = stocks;

                        renderStockTable(stocks);
                        updateBuyButtonState();
                        renderHoldingsTable(window.holdingsData); // Re-render holdings to update highlight
                    })
                    .catch(error => {
                        console.error('Error fetching stocks:', error.message || error);
                         if (!error.message?.includes("Login required")) {
                            showToast(`Error fetching stocks: ${error.message || 'Unknown Error'}`, 'error');
                         }
                        renderStockTable([]);
                    })
                    .finally(() => setButtonLoading(refreshStocksButton, false));
             }

            function renderStockTable(stocks) {
                const tbody = document.getElementById('stock-table-body');
                if (!tbody) return;
                const purchasedStocks = getPurchasedStocks();
                const paperTradeStocks = getPaperTradeStocks();
                // ---> ADDED: Get current holding base symbols for highlighting <---
                const holdingBaseSymbols = new Set(window.holdingsData.map(holding => holding.symbol ? holding.symbol.split('-')[0] : ''));

                 const isLoading = refreshStocksButton?.disabled ?? false;

                if (stocks.length === 0 && !isLoading) {
                     tbody.innerHTML = `<tr><td colspan="6" class="text-center text-muted py-3">No stocks identified currently.</td></tr>`;
                     return;
                 }
                 if(stocks.length === 0 && isLoading) {
                    renderTableSkeletons('stock-table-body', 6, 3);
                    return;
                 }

                const currentSortHeader = document.querySelector('#stock-table th.asc, #stock-table th.desc');
                const sortColumn = currentSortHeader?.dataset.column;
                const sortAsc = currentSortHeader?.classList.contains('asc');

                tbody.innerHTML = stocks.map((stock, index) => {
                    const baseSymbol = stock.symbol || 'N/A';
                    const companyName = stock.name || 'Unknown Company';
                    const isPurchased = purchasedStocks.has(baseSymbol);
                    const isPaperTrade = paperTradeStocks.has(baseSymbol);
                    const price = typeof stock.current_price === 'number' ? stock.current_price.toFixed(2) : 'N/A';
                    const dateIdentified = stock.date || 'N/A';
                    // ---> ADDED: Check if this identified stock is also in holdings <---
                    const isAlsoHolding = holdingBaseSymbols.has(baseSymbol);

                    const changeRaw = stock.percentage_change;
                    let changeDisplay = 'N/A';
                    let pnlClass = '';
                    if (changeRaw !== null && changeRaw !== undefined && changeRaw !== '') {
                        const changeNum = parseFloat(changeRaw);
                        if (!isNaN(changeNum)) {
                            changeDisplay = `${changeNum.toFixed(2)}%`;
                            pnlClass = changeNum >= 0 ? 'profit-positive' : 'profit-negative';
                        } else { changeDisplay = 'Error'; }
                    }

                    // ---> MODIFIED: Add highlight-identified class if isAlsoHolding is true <---
                    return `
                    <tr class="stock-row content-fade-in ${isAlsoHolding ? 'highlight-identified' : ''}" data-symbol="${baseSymbol}" style="animation-delay: ${index * 0.03}s">
                        <td style="width: 5%;">
                             <input type="checkbox" class="form-check-input stock-select"
                                data-name="${companyName}" data-symbol="${baseSymbol}" data-price="${stock.current_price || 0}"
                                > <!-- REMOVED disabled attribute -->
                        </td>
                        <td class="col-company" title="${companyName}">${companyName}</td>
                        <td class="col-symbol-link">
                            <a href="https://www.tradingview.com/chart/?symbol=NSE%3A${baseSymbol}" target="_blank" class="stock-badge">${baseSymbol}</a>
                             ${isPurchased ? '<i class="fas fa-check-circle purchased-icon" title="Already Purchased (Real)"></i>' : ''}
                             ${isPaperTrade ? '<i class="fas fa-file-alt paper-trade-icon" title="Already Purchased (Paper)"></i>' : ''}
                        </td>
                        <td>₹${price}</td>
                        <td><span class="${pnlClass}">${changeDisplay}</span></td>
                        <td class="text-muted small">${dateIdentified}</td>
                    </tr>`;
                }).join('');

                 if (sortColumn) {
                     const headerToMark = document.querySelector(`#stock-table th[data-column="${sortColumn}"]`);
                     if (headerToMark) {
                         headerToMark.classList.add(sortAsc ? 'asc' : 'desc');
                     }
                 }


                tbody.querySelectorAll('.stock-select').forEach(cb => {
                    cb.addEventListener('change', updateBuyButtonState);
                });
                updateBuyButtonState();
            }

            // --- Holdings Table: Timers and Update Logic ---
            let fullHoldingsRefreshTimeout; // For less frequent full refresh
            let partialHoldingsRefreshInterval; // For frequent price updates
            const holdingsRefreshTimerEl = document.getElementById('holdings-refresh-timer');
            const holdingsRefreshDialEl = document.getElementById('holdings-refresh-dial');
            const PARTIAL_HOLDINGS_REFRESH_INTERVAL = 2000; // 2 seconds
            const FULL_HOLDINGS_REFRESH_INTERVAL = 120000; // 120 seconds
            let partialHoldingsTimeRemaining = PARTIAL_HOLDINGS_REFRESH_INTERVAL / 1000;

            function startPartialHoldingsRefreshTimer() {
                clearInterval(partialHoldingsRefreshInterval);
                if (!holdingsRefreshTimerEl || !holdingsRefreshDialEl) return;

                partialHoldingsTimeRemaining = PARTIAL_HOLDINGS_REFRESH_INTERVAL / 1000;
                holdingsRefreshDialEl.classList.remove('hidden');
                holdingsRefreshTimerEl.textContent = partialHoldingsTimeRemaining;

                partialHoldingsRefreshInterval = setInterval(() => {
                    partialHoldingsTimeRemaining--;
                    if (holdingsRefreshTimerEl) holdingsRefreshTimerEl.textContent = partialHoldingsTimeRemaining;
                    if (partialHoldingsTimeRemaining <= 0) {
                        clearInterval(partialHoldingsRefreshInterval); // Stop this interval
                        if (holdingsRefreshDialEl) holdingsRefreshDialEl.classList.add('hidden');
                        partialHoldingsUpdate(); // Perform partial update, which will restart this timer
                    }
                }, 1000);
            }

            function updateHoldingsDisplay() { // This is now for FULL refresh
                clearTimeout(fullHoldingsRefreshTimeout); // Clear any pending full refresh

                // If a partial refresh is active, let it complete or clear its interval
                // to avoid conflicts during full render.
                clearInterval(partialHoldingsRefreshInterval);
                if (holdingsRefreshDialEl) holdingsRefreshDialEl.classList.add('hidden');

                fetch('/api/holdings') // Fetches full structure
                    .then(async res => {
                        if (!res.ok) throw await handleFetchError(res, "fetch full holdings");
                        return res.json();
                    })
                    .then(holdingsResponse => {
                        if (holdingsResponse.error) throw new Error(holdingsResponse.error);

                        const holdings = holdingsResponse.holdings || [];
                        // Calculate P&L for each holding (full data from backend)
                        holdings.forEach(h => {
                            const avgPrice = typeof h.average_price === 'number' ? h.average_price : 0;
                            const quantity = typeof h.quantity === 'number' ? h.quantity : 0;
                            const currentPrice = typeof h.current_price === 'number' ? h.current_price : avgPrice;
                            const lastDayPrice = typeof h.last_day_price === 'number' ? h.last_day_price : avgPrice;

                            h.invested = avgPrice * quantity;
                            h.overall_pl_amount = (currentPrice - avgPrice) * quantity;
                            h.overall_pl_percent = h.invested !== 0 ? (h.overall_pl_amount / h.invested * 100) : 0;
                            h.days_pl_amount = (currentPrice - lastDayPrice) * quantity;
                            const invested_day = lastDayPrice * quantity;
                            h.days_pl_percent = invested_day !== 0 ? (h.days_pl_amount / invested_day * 100) : 0;
                        });

                        holdings.sort((a, b) => (b.invested || 0) - (a.invested || 0));
                        window.holdingsData = holdings; // Update global store with full data

                        renderHoldingsTable(window.holdingsData); // Full re-render
                        updateOverallPLStats(); // Update dashboard stats based on new full data
                        updateTotalActiveInvestment();

                        if (isInitialHoldingsLoad) {
                           isInitialHoldingsLoad = false;
                        }
                        // Start the partial refresh timer after full load/render
                        startPartialHoldingsRefreshTimer();
                        renderStockTable(window.stocksData); // Re-render identified stocks to update highlights
                    })
                    .catch(error => {
                        console.error('Error during full holdings update:', error.message || error);
                        if (isInitialHoldingsLoad) renderHoldingsTable([]); // Clear table on initial load error
                        updateOverallPLStats([]); // Show zero/default stats
                        updateTotalActiveInvestment();
                        document.title = "Finvasia | Error";
                        if (!error.message?.includes("Login required")) {
                            showToast(`Could not fetch holdings: ${error.message || 'Unknown Error'}`, 'error');
                        }
                         // Still attempt to start partial updates, might recover if temporary issue
                         if(isInitialHoldingsLoad) isInitialHoldingsLoad = false;
                         startPartialHoldingsRefreshTimer();
                    })
                    .finally(() => {
                        // Schedule the next full refresh
                        fullHoldingsRefreshTimeout = setTimeout(updateHoldingsDisplay, FULL_HOLDINGS_REFRESH_INTERVAL);
                    });
            }

            async function partialHoldingsUpdate() {
                if (window.holdingsData.length === 0) {
                    console.log("PartialHoldingsUpdate: No holdings data to update prices for. Restarting timer.");
                    startPartialHoldingsRefreshTimer(); // Restart timer and wait for full refresh
                    return;
                }

                const symbolsToFetch = window.holdingsData.map(h => h.symbol).filter(s => s);
                if (symbolsToFetch.length === 0) {
                    startPartialHoldingsRefreshTimer();
                    return;
                }

                try {
                    const response = await fetch('/api/holdings/current_prices', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ symbols: symbolsToFetch })
                    });
                    if (!response.ok) throw await handleFetchError(response, "fetch partial holdings prices");

                    const data = await response.json();
                    const pricesMap = data.prices || {};
                    window.activeSymbols = data.active_trail_symbols || []; // Update active trailed symbols

                    let newTotalOverallPlAmount = 0;
                    let newTotalInvestedForOverall = 0;
                    let newTotalDaysPlAmount = 0;
                    let newTotalInitialInvestmentDay = 0;

                    window.holdingsData.forEach(holding => {
                        const newPrice = pricesMap[holding.symbol];
                        if (typeof newPrice === 'number') {
                            holding.current_price = newPrice; // Update current price in the global store
                        }
                        // Recalculate P&L metrics based on potentially updated current_price
                        const avgPrice = holding.average_price || 0;
                        const quantity = holding.quantity || 0;
                        const currentPrice = holding.current_price || avgPrice; // Use updated or existing
                        const lastDayPrice = typeof holding.last_day_price === 'number' ? holding.last_day_price : avgPrice;

                        holding.invested = avgPrice * quantity; // This doesn't change with price
                        holding.overall_pl_amount = (currentPrice - avgPrice) * quantity;
                        holding.overall_pl_percent = holding.invested !== 0 ? (holding.overall_pl_amount / holding.invested * 100) : 0;
                        holding.days_pl_amount = (currentPrice - lastDayPrice) * quantity;
                        const invested_day = lastDayPrice * quantity;
                        holding.days_pl_percent = invested_day !== 0 ? (holding.days_pl_amount / invested_day * 100) : 0;

                        // Accumulate for dashboard stats
                        newTotalOverallPlAmount += holding.overall_pl_amount;
                        newTotalInvestedForOverall += holding.invested; // Sum of original invested amounts
                        newTotalDaysPlAmount += holding.days_pl_amount;
                        newTotalInitialInvestmentDay += invested_day;


                        // Update DOM for this specific row
                        const row = document.querySelector(`#holdings-body tr[data-symbol-row="${holding.symbol}"]`);
                        if (row) {
                            const currentPriceCell = row.querySelector('.col-current-price');
                            if (currentPriceCell) currentPriceCell.textContent = `₹${currentPrice.toFixed(2)}`;

                            const daysPlAmountCell = row.querySelector('.col-days-pl-amount span');
                            if (daysPlAmountCell) {
                                daysPlAmountCell.textContent = `₹${holding.days_pl_amount.toFixed(2)}`;
                                daysPlAmountCell.className = holding.days_pl_amount >= 0 ? 'profit-positive' : 'profit-negative';
                            }
                            const daysPlPercentCell = row.querySelector('.col-days-pl-percent span');
                            if (daysPlPercentCell) {
                                daysPlPercentCell.textContent = `${holding.days_pl_percent.toFixed(2)}%`;
                                daysPlPercentCell.className = holding.days_pl_percent >= 0 ? 'profit-positive' : 'profit-negative';
                            }
                            const overallPlAmountCell = row.querySelector('.col-overall-pl-amount span');
                            if (overallPlAmountCell) {
                                overallPlAmountCell.textContent = `₹${holding.overall_pl_amount.toFixed(2)}`;
                                overallPlAmountCell.className = holding.overall_pl_amount >= 0 ? 'profit-positive' : 'profit-negative';
                            }
                            const overallPlPercentCell = row.querySelector('.col-overall-pl-percent span');
                            if (overallPlPercentCell) {
                                overallPlPercentCell.textContent = `${holding.overall_pl_percent.toFixed(2)}%`;
                                overallPlPercentCell.className = holding.overall_pl_percent >= 0 ? 'profit-positive' : 'profit-negative';
                            }

                            // Update Buy More button data-price
                            const buyMoreBtn = row.querySelector('.buy-more');
                            if (buyMoreBtn) buyMoreBtn.dataset.price = currentPrice;

                            // Update Sell Now button visibility
                            const sellNowBtn = row.querySelector('.sell-now');
                            if (sellNowBtn) {
                                sellNowBtn.style.display = holding.overall_pl_percent > 0.5 ? '' : 'none';
                            }
                             // Update flashing class for stock badge
                            const badge = row.querySelector('.stock-badge');
                            if (badge) {
                                const isTrailed = window.activeSymbols.includes(holding.symbol);
                                if (isTrailed) badge.classList.add('flashing');
                                else badge.classList.remove('flashing');
                                badge.title = `${holding.symbol}${isTrailed ? ' (Trailing Stop Loss Active)' : ''}`;
                            }
                        }
                    });

                    // Update overall dashboard stats
                    updateOverallPLStats(window.holdingsData); // Pass current state of holdingsData
                    // updateTotalActiveInvestment(); // This mainly depends on avg_price, not current_price, so might not need frequent updates here

                } catch (error) {
                    console.error('Error during partial holdings update:', error.message || error);
                    if (!error.message?.includes("Login required")) {
                         showToast(`Holdings price update failed: ${error.message || 'Unknown Error'}`, 'error', 2000);
                    }
                } finally {
                    startPartialHoldingsRefreshTimer(); // Restart timer for next partial update
                }
            }


            function renderHoldingsTable(holdings) {
                const tbody = document.getElementById('holdings-body');
                if (!tbody) return;

                const identifiedSymbols = new Set(window.stocksData.map(stock => stock.symbol ? stock.symbol.split('-')[0] : ''));
                const dontSellSymbols = new Set(window.dontSellData.symbols || []);

                if (holdings.length === 0 && !isInitialHoldingsLoad) { // Only show "No holdings" if not initial skeleton phase
                    tbody.innerHTML = `<tr><td colspan="12" class="text-center text-muted py-3">No holdings found.</td></tr>`;
                    return;
                }
                if (isInitialHoldingsLoad && holdings.length === 0) { // If initial load and still no data, keep skeletons
                    renderTableSkeletons('holdings-body', 12, 2);
                    return;
                }


                 const currentSortHeader = document.querySelector('#holdings-table th.asc, #holdings-table th.desc');
                 const sortColumn = currentSortHeader?.dataset.column;
                 const sortAsc = currentSortHeader?.classList.contains('asc');


                 tbody.innerHTML = holdings.map((holding, index) => {
                    const tradingSymbol = holding.symbol || 'N/A';
                    const baseSymbol = tradingSymbol.split('-')[0];
                    const isIdentified = identifiedSymbols.has(baseSymbol); // Check if holding's base symbol is in identified list
                    const quantity = holding.quantity || 0;
                    const currentPrice = holding.current_price || 0; // Already calculated in updateHoldingsDisplay or partialHoldingsUpdate
                    const days_pl_amount = holding.days_pl_amount || 0;
                    const days_pl_percent = holding.days_pl_percent || 0;
                    const avgPrice = holding.average_price || 0;
                    const invested = holding.invested || 0;
                    const overall_pl_amount = holding.overall_pl_amount || 0;
                    const overall_pl_percent = holding.overall_pl_percent || 0;


                    const daysPlAmountClass = days_pl_amount >= 0 ? 'profit-positive' : 'profit-negative';
                    const daysPlPercentClass = days_pl_percent >= 0 ? 'profit-positive' : 'profit-negative';
                    const plAmountClass = overall_pl_amount >= 0 ? 'profit-positive' : 'profit-negative';
                    const plPercentClass = overall_pl_percent >= 0 ? 'profit-positive' : 'profit-negative';

                    const showSellButton = overall_pl_percent > 0.5;
                    const isChecked = dontSellSymbols.has(tradingSymbol);
                    const isTrailed = window.activeSymbols.includes(tradingSymbol); // Check against global activeSymbols

                    // ---> MODIFIED: Add highlight-identified class if isIdentified is true <---
                    return `
                    <tr class="stock-row content-fade-in ${isIdentified ? 'highlight-identified' : ''}" style="animation-delay: ${index * 0.03}s" data-symbol-row="${tradingSymbol}">
                        <td style="width: 5%;">
                             <input type="checkbox" class="form-check-input dont-sell-checkbox"
                                data-symbol="${tradingSymbol}" ${isChecked ? 'checked' : ''}
                                title="Check to prevent auto-selling and trailing stop loss for this stock">
                        </td>
                        <td class="col-symbol-link">
                             <a href="https://www.tradingview.com/chart/?symbol=NSE%3A${baseSymbol}" target="_blank"
                                class="stock-badge ${isTrailed ? 'flashing' : ''}"
                                title="${tradingSymbol}${isTrailed ? ' (Trailing Stop Loss Active)' : ''}">
                                ${tradingSymbol}
                            </a>
                        </td>
                        <td>${quantity}</td>
                        <td class="col-current-price">₹${currentPrice.toFixed(2)}</td>
                        <td class="col-days-pl-amount"><span class="${daysPlAmountClass}">₹${days_pl_amount.toFixed(2)}</span></td>
                        <td class="col-days-pl-percent"><span class="${daysPlPercentClass}">${days_pl_percent.toFixed(2)}%</span></td>
                        <td>₹${avgPrice.toFixed(2)}</td>
                        <td>₹${invested.toFixed(2)}</td>
                        <td class="col-overall-pl-amount"><span class="${plAmountClass}">₹${overall_pl_amount.toFixed(2)}</span></td>
                        <td class="col-overall-pl-percent">
                            <span class="${plPercentClass}" title="${(window.buyOnDipState && window.buyOnDipState[tradingSymbol] && window.buyOnDipState[tradingSymbol].triggered_thresholds && window.buyOnDipState[tradingSymbol].triggered_thresholds.length > 0) ? 'Triggered Dips: ' + window.buyOnDipState[tradingSymbol].triggered_thresholds.map(t => t + '%').join(', ') : 'No triggered dips'}">
                                ${overall_pl_percent.toFixed(2)}%
                            </span>
                        </td>
                        <td class="actions-cell">
                             <button class="btn btn-sm btn-danger sell-now hide-text-mobile" data-symbol="${tradingSymbol}" title="Sell All Shares Immediately" style="display: ${showSellButton ? '' : 'none'}">
                                <i class="fas fa-dollar-sign"></i> <span class="d-none d-lg-inline">Sell</span>
                            </button>
                        </td>
                        <td class="instant-buy-cell">
                            <div class="d-flex justify-content-center align-items-center instant-buy-group-wrapper">
                                <div class="input-group input-group-sm" style="width: auto;">
                                    <input type="number" class="form-control quantity-input text-center" value="1" min="1" style="max-width: 50px;" aria-label="Quantity to buy">
                                    <button class="btn btn-success buy-more"
                                            data-symbol="${tradingSymbol}"
                                            data-name="${baseSymbol}"
                                            data-price="${currentPrice}" title="Buy More Shares"> <!-- data-price updated here -->
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>`;
                 }).join('');

                 if (sortColumn) {
                     const headerToMark = document.querySelector(`#holdings-table th[data-column="${sortColumn}"]`);
                     if (headerToMark) {
                         headerToMark.classList.add(sortAsc ? 'asc' : 'desc');
                     }
                 }


                tbody.querySelectorAll('.dont-sell-checkbox').forEach(cb => cb.addEventListener('change', handleDontSellChange));
                tbody.querySelectorAll('.sell-now').forEach(btn => btn.addEventListener('click', handleSellNowClick));
                tbody.querySelectorAll('.buy-more').forEach(btn => btn.addEventListener('click', handleBuyMoreClick));
            }


            function handleDontSellChange(event) {
                const originalCheckbox = event.target; // The checkbox element the user clicked
                const tradingSymbol = originalCheckbox.dataset.symbol;
                // The 'checked' state of originalCheckbox is the state *after* the user's click
                const intendedAction = originalCheckbox.checked ? 'add' : 'remove';

                if (!tradingSymbol || tradingSymbol === 'N/A') return;

                originalCheckbox.disabled = true; // Disable the one clicked

                fetch('/api/dont_sell', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol: tradingSymbol, action: intendedAction })
                })
                .then(async response => {
                    if (!response.ok) throw await handleFetchError(response, `update Don't Sell list for ${tradingSymbol}`);
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        showToast(`${tradingSymbol} ${intendedAction === 'add' ? 'added to' : 'removed from'} "Don't Sell" list.`, 'info');
                        window.dontSellData.symbols = data.symbols || []; // Update global state

                        // Re-select the checkbox in the DOM in case it was re-rendered by another process
                        const currentCheckboxInDOM = document.querySelector(`#holdings-body .dont-sell-checkbox[data-symbol="${tradingSymbol}"]`);
                        if (currentCheckboxInDOM) {
                            currentCheckboxInDOM.checked = (intendedAction === 'add'); // Enforce the intended state
                        }

                        // Original badge update logic
                        const badge = document.querySelector(`#holdings-body .stock-badge[title*="${tradingSymbol}"]`);
                        if (badge) {
                            if (intendedAction === 'add') { // Added to "Don't Sell"
                                badge.classList.remove('flashing');
                                badge.title = `${tradingSymbol}`;
                                window.activeSymbols = window.activeSymbols.filter(s => s !== tradingSymbol);
                            } else { // Removed from "Don't Sell" - trailing might activate on next update cycle
                                const holding = window.holdingsData.find(h => h.symbol === tradingSymbol);
                                if (holding) {
                                    // Existing logic for when a holding is removed from "Don't Sell"
                                }
                                badge.title = `${tradingSymbol}`;
                            }
                        }
                    } else { throw new Error(data.message || 'Update failed'); }
                })
                .catch(error => {
                    showToast(`Error updating "Don't Sell" for ${tradingSymbol}: ${error.message || error}`, 'error');
                    // Revert the checkbox state in the DOM to what it was *before* the user's click
                    const currentCheckboxInDOM = document.querySelector(`#holdings-body .dont-sell-checkbox[data-symbol="${tradingSymbol}"]`);
                    if (currentCheckboxInDOM) {
                        currentCheckboxInDOM.checked = !(intendedAction === 'add');
                    }
                })
                .finally(() => {
                    // Re-select the checkbox to ensure we're enabling the current one in the DOM
                    const currentCheckboxInDOM = document.querySelector(`#holdings-body .dont-sell-checkbox[data-symbol="${tradingSymbol}"]`);
                    if (currentCheckboxInDOM) {
                        currentCheckboxInDOM.disabled = false;
                    } else {
                        if (originalCheckbox && typeof originalCheckbox.disabled !== 'undefined') {
                             originalCheckbox.disabled = false;
                        }
                    }
                });
            }

             function handleSellNowClick(event) {
                 const btn = event.currentTarget;
                 const tradingSymbol = btn.dataset.symbol;
                 const originalBtnHTML = btn.innerHTML;

                 if (!tradingSymbol || tradingSymbol === 'N/A') {
                     showToast('Cannot sell: Invalid symbol.', 'error'); return;
                 }

                 Swal.fire({
                    title: `Sell ALL ${tradingSymbol}?`,
                    text: "This action cannot be undone.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: 'var(--danger)',
                    cancelButtonColor: 'var(--muted)',
                    confirmButtonText: 'Yes, Sell Now!',
                    customClass: { popup: `swal2-${document.documentElement.getAttribute('data-bs-theme') || 'light'}` }
                 }).then((result) => {
                    if (result.isConfirmed) {
                        setButtonLoading(btn, true);
                        fetch('/api/manual_sell', {
                            method: 'POST', headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ symbol: tradingSymbol })
                        })
                        .then(async response => {
                             if (!response.ok) throw await handleFetchError(response, `sell ${tradingSymbol}`);
                             return response.json();
                        })
                        .then(data => {
                            if (data.status === 'success') {
                                showToast(data.message || `Sell order placed for ${tradingSymbol}`, 'success');
                                // Trigger data refresh after successful sell
                                updateCashDisplay();
                                updatePositionsDisplay();
                                updateHoldingsDisplay(); // This will do a full refresh
                            } else { throw new Error(data.error || data.message || `Failed to sell ${tradingSymbol}`); }
                        })
                        .catch(error => {
                            showToast(`Error selling ${tradingSymbol}: ${error.message || error}`, 'error');
                            setButtonLoading(btn, false, originalBtnHTML); // Restore button only on error
                        });
                        // Note: Button is not restored on success, as the row should disappear after full refresh
                    }
                 });
             }

            // Function to programmatically add a symbol to the "Don't Sell" list
            async function addToDontSellList(tradingSymbol) {
                if (!tradingSymbol) return false;
                const checkbox = document.querySelector(`#holdings-body .dont-sell-checkbox[data-symbol="${tradingSymbol}"]`);
                const dontSellSet = new Set(window.dontSellData.symbols || []);

                // If already in the list or checkbox exists and is checked, no action needed
                if (dontSellSet.has(tradingSymbol) || (checkbox && checkbox.checked)) {
                    console.log(`${tradingSymbol} is already marked as 'Hold'.`);
                    return true;
                }

                console.log(`Attempting to programmatically add ${tradingSymbol} to Don't Sell list.`);
                if (checkbox) checkbox.disabled = true; // Disable checkbox during API call

                 try {
                     const response = await fetch('/api/dont_sell', {
                         method: 'POST', headers: { 'Content-Type': 'application/json' },
                         body: JSON.stringify({ symbol: tradingSymbol, action: 'add' })
                     });
                     if (!response.ok) {
                         const err = await handleFetchError(response, `programmatically add ${tradingSymbol} to Don't Sell`);
                         throw err;
                     }
                     const data = await response.json();
                     if (data.status === 'success') {
                         // Server reported success for 'add' action.
                         // Ensure client-side list reflects this, even if data.symbols was unexpectedly stale.
                         let serverSymbols = new Set(data.symbols || []);
                         serverSymbols.add(tradingSymbol); // Ensure the symbol is in the set
                         window.dontSellData.symbols = Array.from(serverSymbols);

                         // Update UI checkbox if it exists (for immediate feedback before re-render)
                         if (checkbox) checkbox.checked = true;

                         // Update flashing state
                         const badge = document.querySelector(`#holdings-body .stock-badge[title*="${tradingSymbol}"]`);
                         if (badge) {
                             badge.classList.remove('flashing');
                             badge.title = `${tradingSymbol}`;
                         }
                         window.activeSymbols = window.activeSymbols.filter(s => s !== tradingSymbol); // Remove from active tracking

                         console.log(`${tradingSymbol} processed for Don't Sell list. Server response symbols count: ${(data.symbols || []).length}, Client symbols count: ${window.dontSellData.symbols.length}`);
                         return true;
                     } else {
                         throw new Error(data.message || 'Failed to add to Don\'t Sell list.');
                     }
                 } catch (error) {
                     console.error(`Error programmatically adding ${tradingSymbol} to Don't Sell:`, error.message || error);
                     showToast(`Could not auto-add ${tradingSymbol} to Hold list: ${error.message || 'Unknown error'}`, 'warning', 4000);
                     // Do not revert checkbox state here, as it wasn't checked before
                     return false;
                 } finally {
                     if (checkbox) checkbox.disabled = false; // Re-enable checkbox
                 }
            }


            async function handleBuyMoreClick(event) {
                 const btn = event.currentTarget;
                 const tradingSymbol = btn.dataset.symbol;
                 const baseSymbol = btn.dataset.name;
                 const price = parseFloat(btn.dataset.price); // Use current price for display, backend uses market order
                 const quantityInput = btn.closest('.input-group').querySelector('.quantity-input');
                 const quantity = parseInt(quantityInput.value) || 1;
                 const originalBtnHTML = btn.innerHTML;

                 if (!tradingSymbol || tradingSymbol === 'N/A') { showToast('Cannot buy: Invalid symbol.', 'error'); return; }
                 if (quantity < 1) { showToast('Please enter a valid quantity (1 or more)', 'error'); return; }

                 setButtonLoading(btn, true);
                 if(quantityInput) quantityInput.disabled = true;

                 try {
                     // Step 1: Execute the buy order
                     const response = await fetch('/buy_stocks', {
                        method: 'POST', headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                             stocks: [{ symbol: baseSymbol, name: baseSymbol }],
                             quantity: quantity,
                             buy_as_paper_trade: false // Buying more is always real trade
                         })
                     });
                      if (!response.ok) throw await handleFetchError(response, `buy ${quantity} ${tradingSymbol}`);

                      const data = await response.json();
                      if (data.error) throw new Error(data.error);

                     const resultForStock = data.results?.find(r => r.symbol === tradingSymbol);

                     if (resultForStock && !resultForStock.error && resultForStock.order_response?.stat === 'Ok') {
                         // Step 2: If buy was successful, add the stock to the "Don't Sell" list
                         const addedToHold = await addToDontSellList(tradingSymbol);
                         const holdMessage = addedToHold ? ' and added to Hold list' : '';

                         showToast(`Bought ${quantity} ${tradingSymbol}${holdMessage}. Order price may vary.`, 'success', 6000);

                         // Step 3: Update UI elements
                         const identifiedStockCheckbox = document.querySelector(`#stock-table-body .stock-select[data-symbol="${baseSymbol}"]`);
                         if (identifiedStockCheckbox) {
                             identifiedStockCheckbox.checked = false;
                         }
                         updateBuyButtonState();

                         // Step 4: Refresh relevant data sections (ordered for data consistency)
                         updateCashDisplay(); // Update cash first

                         await fetchDontSellConfig();    // Ensure "Don't Sell" list is fresh
                         await updateHoldingsDisplay();  // Fetches holdings & re-renders holdings table (which uses fresh dontSellData and also calls renderStockTable)
                         await updatePositionsDisplay(); // Refresh positions table (await if async)
                         // renderStockTable(window.stocksData); // This call might be redundant as updateHoldingsDisplay should cover it. Kept commented for now, can be removed if confirmed redundant. If specific updates are needed for stock table beyond what holdings update does, it can be reinstated.

                     } else {
                         const errorMsg = resultForStock?.error || data.message || 'Buy operation failed for '+tradingSymbol;
                         throw new Error(errorMsg);
                     }
                 } catch (error) {
                     showToast(`Error buying ${tradingSymbol}: ${error.message || error}`, 'error');
                 } finally {
                     setButtonLoading(btn, false, originalBtnHTML);
                     if(quantityInput) quantityInput.disabled = false;
                     if(quantityInput) quantityInput.value = 1;
                 }
            }


            // --- Identified Stocks Actions ---
            const investmentAmountInput = document.getElementById('investment-amount');
            const updateAmountButton = document.getElementById('update-amount');
            const buyButton = document.getElementById('buy-selected');
            const paperTradeCheckbox = document.getElementById('buy-as-paper-trade');
            const autoBuyToggle = document.getElementById('auto-buy-toggle');

            if (updateAmountButton && investmentAmountInput) {
                updateAmountButton.addEventListener('click', () => {
                    const amount = parseInt(investmentAmountInput.value);
                    if (isNaN(amount) || amount < 500) {
                        showToast('Minimum investment amount is ₹500', 'error');
                        return;
                    }
                    const originalBtnHTML = updateAmountButton.innerHTML;
                    setButtonLoading(updateAmountButton, true);

                    fetch('/api/investment_amount', {
                        method: 'POST', headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ amount: amount })
                    })
                    .then(async response => {
                        if (!response.ok) throw await handleFetchError(response, "update investment amount");
                        return response.json();
                    })
                    .then(data => {
                        if (data.status === 'success') {
                            window.configData.investment_amount = data.amount;
                            showToast(`Amount preference updated to ₹${data.amount}`, 'success');
                        } else { throw new Error(data.message || 'Failed to update amount'); }
                    })
                    .catch(error => showToast(`Error updating amount: ${error.message || error}`, 'error'))
                    .finally(() => setButtonLoading(updateAmountButton, false, originalBtnHTML));
                });
            }

            if(autoBuyToggle) {
                autoBuyToggle.addEventListener('change', () => {
                    const isEnabled = autoBuyToggle.checked;
                    autoBuyToggle.disabled = true;

                    fetch('/api/auto_buy_config', {
                         method: 'POST', headers: { 'Content-Type': 'application/json' },
                         body: JSON.stringify({ enabled: isEnabled })
                    })
                    .then(async response => {
                        if (!response.ok) throw await handleFetchError(response, "update auto-buy config");
                        return response.json();
                    })
                    .then(data => {
                        if (data.status === 'success') {
                            window.configData.auto_buy_enabled = data.enabled;
                            showToast(`Auto-buy ${data.enabled ? 'enabled' : 'disabled'}`, 'success');
                        } else { throw new Error(data.message || 'Failed to update auto-buy status'); }
                    })
                    .catch(error => {
                        showToast(`Error updating auto-buy: ${error.message || error}`, 'error');
                        autoBuyToggle.checked = !isEnabled; // Revert on error
                    })
                    .finally(() => { if (autoBuyToggle) autoBuyToggle.disabled = document.getElementById('goAutoPilotToggle')?.checked || false; });
                });
            }


            function updateBuyButtonState() {
                if (!buyButton) return;
                const selectedCheckboxes = document.querySelectorAll('.stock-select:checked');
                const count = selectedCheckboxes.length;
                buyButton.disabled = count === 0;
                const buyButtonSpan = buyButton.querySelector('span');
                if (buyButtonSpan) {
                    buyButtonSpan.textContent = count > 0 ? `Buy ${count} Selected` : 'Buy Selected';
                }
            }


             if (buyButton) {
                 buyButton.addEventListener('click', async () => {
                     const amountPerStock = parseInt(investmentAmountInput?.value) || window.configData.investment_amount;
                     const buyAsPaperTrade = paperTradeCheckbox?.checked || false;
                     const originalBtnHTML = buyButton.innerHTML;
                     const selectedStocks = Array.from(document.querySelectorAll('.stock-select:checked')).map(checkbox => ({
                        name: checkbox.dataset.name,
                        symbol: checkbox.dataset.symbol,
                        price: parseFloat(checkbox.dataset.price)
                     }));

                     if (selectedStocks.length === 0) return;

                     const affordableStocksInfo = selectedStocks.filter(stock => {
                         if (!stock.price || stock.price <= 0) {
                             showToast(`Skipping ${stock.symbol}: Invalid price data.`, 'warning', 4000);
                             return false;
                         }
                         const qty = Math.floor(amountPerStock / stock.price);
                         if (qty < 1) {
                             showToast(`Skipping ${stock.symbol}: Price (₹${stock.price.toFixed(2)}) too high for amount (₹${amountPerStock})`, 'warning', 4000);
                             return false;
                         }
                         return true;
                     });

                     selectedStocks.forEach(s => {
                         if (!affordableStocksInfo.some(a => a.symbol === s.symbol)) {
                             const cb = document.querySelector(`.stock-select[data-symbol="${s.symbol}"]`);
                             if (cb) cb.checked = false;
                         }
                     });

                     if (affordableStocksInfo.length === 0) {
                        showToast('No selected stocks are affordable or have valid prices.', 'error');
                        updateBuyButtonState();
                        return;
                     }

                     setButtonLoading(buyButton, true);
                     document.querySelectorAll('.stock-select').forEach(cb => cb.disabled = true);

                     try {
                         const response = await fetch('/buy_stocks', {
                             method: 'POST', headers: { 'Content-Type': 'application/json' },
                             body: JSON.stringify({
                                 stocks: affordableStocksInfo.map(s => ({ symbol: s.symbol, name: s.name })),
                                 amount: amountPerStock,
                                 buy_as_paper_trade: buyAsPaperTrade
                             })
                         });
                         if (!response.ok) throw await handleFetchError(response, "buy selected stocks");

                         const data = await response.json();
                         if (data.error) throw new Error(data.error);

                         let successCount = 0;
                         const purchasedSymbolsReal = new Set();
                         const purchasedSymbolsPaper = new Set();
                         const successfullyBoughtTradingSymbols = [];

                         if (data.results && Array.isArray(data.results)) {
                             for (const result of data.results) {
                                 const purchasedInfo = data.purchased_info?.find(p => p.symbol === result.symbol);
                                 const baseSymbolFromResult = result.symbol ? result.symbol.split('-')[0] : null;
                                 const qty = purchasedInfo?.quantity || result.quantity || 'N/A';
                                 const price = purchasedInfo?.price?.toFixed(2) || result.price?.toFixed(2) || 'N/A';
                                 const displaySymbol = result.symbol || 'Unknown';
                                 const checkbox = document.querySelector(`#stock-table-body .stock-select[data-symbol="${baseSymbolFromResult}"]`);

                                 if (!result.error && (result.order_response?.stat === 'Ok' || result.paper_trade)) {
                                     successCount++;
                                     showToast(`Bought ${qty} ${displaySymbol} @ ₹${price} ${buyAsPaperTrade ? '(Paper)' : ''}`, 'success');
                                     if (baseSymbolFromResult) {
                                         if (buyAsPaperTrade) {
                                             purchasedSymbolsPaper.add(baseSymbolFromResult);
                                         } else {
                                             purchasedSymbolsReal.add(baseSymbolFromResult);
                                             successfullyBoughtTradingSymbols.push(displaySymbol);
                                         }
                                         if (checkbox) {
                                             checkbox.checked = false;
                                         }
                                     }
                                 } else {
                                     showToast(`Failed ${displaySymbol}: ${result.error || 'Unknown reason'}`, 'error', 7000);
                                 }
                             }

                             if (purchasedSymbolsPaper.size > 0) {
                                 const paperStocks = getPaperTradeStocks();
                                 purchasedSymbolsPaper.forEach(s => paperStocks.add(s));
                                 setPaperTradeStocks(paperStocks);
                             }
                             if (purchasedSymbolsReal.size > 0) {
                                 const realStocks = getPurchasedStocks();
                                 purchasedSymbolsReal.forEach(s => realStocks.add(s));
                                 setPurchasedStocks(realStocks);
                             }

                            for (const tradingSymbol of successfullyBoughtTradingSymbols) {
                                await addToDontSellList(tradingSymbol);
                            }

                             updateCashDisplay();
                             updatePositionsDisplay();
                             updateHoldingsDisplay(); // Full refresh
                             renderStockTable(window.stocksData); // Apply updates

                         } else {
                             throw new Error(data.message || 'Invalid response structure from server.');
                         }
                     } catch (error) {
                         showToast(`Error buying stocks: ${error.message || error}`, 'error');
                     } finally {
                         setButtonLoading(buyButton, false, originalBtnHTML);
                         document.querySelectorAll('.stock-select').forEach(cb => cb.disabled = false);
                         updateBuyButtonState();
                     }
                 });
             }


            function getPurchasedStocks() { return new Set(JSON.parse(localStorage.getItem('purchasedStocks_real') || '[]')); }
            function setPurchasedStocks(set) { localStorage.setItem('purchasedStocks_real', JSON.stringify(Array.from(set))); }
            function getPaperTradeStocks() { return new Set(JSON.parse(localStorage.getItem('purchasedStocks_paper') || '[]')); }
            function setPaperTradeStocks(set) { localStorage.setItem('purchasedStocks_paper', JSON.stringify(Array.from(set))); }

            // --- Sell/Trailing Percentages Update ---
            const sellPercentInput = document.getElementById('sell-percentage');
            const trailingSLPercentInput = document.getElementById('trailing-stop-loss-percentage');
            const updateSellPercentButton = document.getElementById('update-sell-percent');
            const updateTrailingSLPercentButton = document.getElementById('update-trailing-sl-percent');

            if (updateSellPercentButton && sellPercentInput) {
                updateSellPercentButton.addEventListener('click', () => {
                    const percentage = parseFloat(sellPercentInput.value);
                    if (isNaN(percentage) || percentage <= 0) { showToast('Invalid sell trigger percentage (must be > 0)', 'error'); return; }
                    const originalBtnHTML = updateSellPercentButton.innerHTML;
                    setButtonLoading(updateSellPercentButton, true);
                    updatePercentage('/api/sell_percentage', { percentage: percentage }, updateSellPercentButton, sellPercentInput, originalBtnHTML);
                });
            }

            if (updateTrailingSLPercentButton && trailingSLPercentInput) {
                updateTrailingSLPercentButton.addEventListener('click', () => {
                    const percentage = parseFloat(trailingSLPercentInput.value);
                    if (isNaN(percentage) || percentage <= 0) { showToast('Invalid trailing SL percentage (must be > 0)', 'error'); return; }
                    const originalBtnHTML = updateTrailingSLPercentButton.innerHTML;
                    setButtonLoading(updateTrailingSLPercentButton, true);
                    updatePercentage('/api/trailing_sl_percentage', { trailing_stop_loss_percentage: percentage }, updateTrailingSLPercentButton, trailingSLPercentInput, originalBtnHTML);
                });
            }

             function updatePercentage(url, body, button, inputElement, originalBtnHTML) {
                 fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) })
                 .then(async response => {
                    if (!response.ok) throw await handleFetchError(response, "update percentage setting");
                    return response.json();
                 })
                 .then(data => {
                     if (data.status === 'success') {
                         if (data.sell_percentage !== undefined) {
                             showToast(`Sell trigger % updated to ${data.sell_percentage}%`, 'success');
                             inputElement.value = data.sell_percentage;
                             window.dontSellData.sell_percentage = data.sell_percentage;
                             showToast('Trailing stop data cleared due to percentage change.', 'info', 3000);
                         } else if (data.trailing_stop_loss_percentage !== undefined) {
                             showToast(`Trailing SL % updated to ${data.trailing_stop_loss_percentage}%`, 'success');
                             inputElement.value = data.trailing_stop_loss_percentage;
                             window.dontSellData.trailing_stop_loss_percentage = data.trailing_stop_loss_percentage;
                              showToast('Trailing stop data cleared due to percentage change.', 'info', 3000);
                         }
                          window.activeSymbols = [];
                          // Full refresh will re-render and apply changes
                          updateHoldingsDisplay();

                     } else { throw new Error(data.message || 'Invalid response'); }
                 })
                 .catch(error => showToast(`Error updating percentage: ${error.message || error}`, 'error'))
                 .finally(() => setButtonLoading(button, false, originalBtnHTML));
             }


            const reloginButton = document.getElementById('relogin-button');
            if (reloginButton) {
                reloginButton.addEventListener('click', () => {
                    const originalBtnHTML = reloginButton.innerHTML;
                    setButtonLoading(reloginButton, true);
                    fetch('/api/relogin', { method: 'POST' })
                        .then(async response => {
                            if (!response.ok) throw await handleFetchError(response, "relogin");
                            return response.json();
                        })
                        .then(data => {
                            if (data.status === 'success') {
                                showToast(data.message || 'Relogin successful!', 'success');
                                reloginButton.remove();
                                showToast('Refreshing dashboard data...', 'info', 2000);
                                initializeDashboard();
                            } else { throw new Error(data.message || 'Relogin failed.'); }
                        })
                        .catch(error => {
                            showToast(`Relogin error: ${error.message || error}`, 'error');
                            setButtonLoading(reloginButton, false, originalBtnHTML);
                        });
                });
            }

            const toggleBuyOnDipSwitch = document.getElementById('toggleBuyOnDip');
            if (toggleBuyOnDipSwitch) {
                toggleBuyOnDipSwitch.addEventListener('change', function() {
                    updateBuyOnDipConfig(this.checked);
                });
            }

            const autoSellToggleSwitch = document.getElementById('auto-sell-toggle');
            if (autoSellToggleSwitch) {
                autoSellToggleSwitch.addEventListener('change', function() {
                    updateAutoSellConfig(this.checked);
                });
            }

            // --- Sorting ---
            let currentSort = { table: null, column: null, ascending: true };

            document.querySelectorAll('.common-table th[data-column]').forEach(th => {
                th.addEventListener('click', () => {
                    const tableId = th.closest('table').id;
                    const column = th.dataset.column;
                    if (!column) return;

                    const isAscending = (currentSort.table === tableId && currentSort.column === column) ? !currentSort.ascending : true;

                    currentSort = { table: tableId, column: column, ascending: isAscending };
                    sortTable(tableId, column, isAscending);

                    th.closest('thead').querySelectorAll('th').forEach(header => header.classList.remove('asc', 'desc'));
                    th.classList.add(isAscending ? 'asc' : 'desc');
                });
            });

            function sortTable(tableId, column, isAscending) {
                let dataProxy; // Use a proxy or copy of the data for sorting
                let renderFunction;
                let dataStoreRef; // Reference to the original window data store


                if (tableId === 'positions-table') { dataProxy = [...window.positionsData]; renderFunction = renderPositionsTable; dataStoreRef = window.positionsData; }
                else if (tableId === 'stock-table') { dataProxy = [...window.stocksData]; renderFunction = renderStockTable; dataStoreRef = window.stocksData; }
                else if (tableId === 'holdings-table') { dataProxy = [...window.holdingsData]; renderFunction = renderHoldingsTable; dataStoreRef = window.holdingsData; }
                else { return; }

                if (!dataProxy || dataProxy.length === 0) return;

                dataProxy.sort((a, b) => {
                    let valueA = getSortValue(a, column, tableId);
                    let valueB = getSortValue(b, column, tableId);

                    if (valueA == null && valueB == null) return 0;
                    if (valueA == null) return isAscending ? 1 : -1;
                    if (valueB == null) return isAscending ? -1 : 1;

                    if (typeof valueA === 'string' && typeof valueB === 'string') {
                        const comparison = valueA.localeCompare(valueB, undefined, { sensitivity: 'base', numeric: true });
                        return isAscending ? comparison : -comparison;
                    } else if (typeof valueA === 'number' && typeof valueB === 'number') {
                        return isAscending ? valueA - valueB : valueB - valueA;
                    } else {
                        return 0;
                    }
                });

                // Update the original data store with the sorted order if it's a full re-render context
                // For holdings, since partial updates happen, ensure this sort affects window.holdingsData
                // so that subsequent partial updates respect the sort.
                if (dataStoreRef) {
                    dataStoreRef.length = 0; // Clear original array
                    dataStoreRef.push(...dataProxy); // Push sorted items back
                }

                // Re-render the table with sorted data
                // For holdings, renderHoldingsTable will use the now-sorted window.holdingsData
                renderFunction(dataProxy); // Pass the sorted copy for rendering
            }

             function getSortValue(item, column, tableId) {
                 const getBaseSymbol = (sym) => (typeof sym === 'string' ? sym.split('-')[0] : null);
                 const parseFloatSafe = (val) => {
                     if (val === null || val === undefined || val === '' || val === 'N/A') return null;
                     const num = parseFloat(String(val).replace(/[₹%,]/g, ''));
                     return isNaN(num) ? null : num;
                 };

                switch (tableId) {
                    case 'positions-table':
                        switch (column) {
                            case 'tsym': return item.tsym;
                            case 'quantity': return parseFloatSafe(item.quantity);
                            case 'current_price': return parseFloatSafe(item.current_price);
                            case 'avg_price': return parseFloatSafe(item.avg_price);
                            case 'invested': return parseFloatSafe(item.invested);
                            case 'unrealized': return parseFloatSafe(item.unrealized);
                            default: return null;
                        }
                    case 'stock-table':
                         switch (column) {
                            case 'name': return item.name || null;
                            case 'symbol': return getBaseSymbol(item.symbol);
                            case 'current_price': return parseFloatSafe(item.current_price);
                            case 'percentage_change': return parseFloatSafe(item.percentage_change);
                            case 'date':
                                if (!item.date || item.date === 'N/A') return null;
                                try { const dateVal = new Date(item.date); return isNaN(dateVal) ? null : dateVal.getTime(); } catch { return null; }
                            default: return null;
                        }
                    case 'holdings-table': // Ensure these match properties of holding objects in window.holdingsData
                        switch (column) {
                            case 'symbol': return item.symbol;
                            case 'quantity': return parseFloatSafe(item.quantity);
                            case 'current_price': return parseFloatSafe(item.current_price);
                            case 'days_pl_amount': return parseFloatSafe(item.days_pl_amount);
                            case 'days_pl_percent': return parseFloatSafe(item.days_pl_percent);
                            case 'average_price': return parseFloatSafe(item.average_price);
                            case 'invested': return parseFloatSafe(item.invested);
                            case 'overall_pl_amount': return parseFloatSafe(item.overall_pl_amount);
                            case 'overall_pl_percent': return parseFloatSafe(item.overall_pl_percent);
                            default: return null;
                        }
                    default: return null;
                }
            }


            function renderTableSkeletons(tbodyId, cols, rows = 3) {
                const tbody = document.getElementById(tbodyId);
                if (!tbody) return;
                let skeletonHTML = '';
                const defaultCell = `<td><span class="skeleton skeleton-text-short mx-auto"></span></td>`;
                const badgeCell = `<td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>`;
                const companyCell = `<td class="col-company"><span class="skeleton skeleton-text"></span></td>`;
                const checkboxCell = `<td style="width: 5%;"><span class="skeleton skeleton-checkbox mx-auto"></span></td>`;
                const actionsCell = `<td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>`;
                const instantBuyCell = `<td class="instant-buy-cell"><div class="skeleton-instant-buy"><span class="skeleton skeleton-input" style="width:50px; height: calc(1.5em + 0.7rem + 2px);"></span><span class="skeleton skeleton-btn" style="margin-left: 4px;"></span></div></td>`;

                for (let i = 0; i < rows; i++) {
                    skeletonHTML += `<tr class="skeleton-row">`;
                    for (let j = 0; j < cols; j++) {
                        if (tbodyId === 'positions-body') {
                            if (j === 0) skeletonHTML += badgeCell;
                            else if (j === cols - 1) skeletonHTML += actionsCell;
                            else skeletonHTML += defaultCell;
                        } else if (tbodyId === 'stock-table-body') {
                            if (j === 0) skeletonHTML += checkboxCell;
                            else if (j === 1) skeletonHTML += companyCell;
                            else if (j === 2) skeletonHTML += badgeCell;
                            else skeletonHTML += defaultCell;
                        } else if (tbodyId === 'holdings-body') {
                            if (j === 0) skeletonHTML += checkboxCell;
                            else if (j === 1) skeletonHTML += badgeCell;
                            else if (j === cols - 2) skeletonHTML += actionsCell;
                            else if (j === cols - 1) skeletonHTML += instantBuyCell;
                            else skeletonHTML += defaultCell;
                        } else {
                            skeletonHTML += defaultCell;
                        }
                    }
                    skeletonHTML += `</tr>`;
                }
                tbody.innerHTML = skeletonHTML;
            }


            // --- Utility: Set Button Loading State ---
            function setButtonLoading(button, isLoading, defaultHTML = null) {
                if (!button) return;
                if (isLoading) {
                    button.disabled = true;
                    if (!button.dataset.originalHtml) {
                        button.dataset.originalHtml = button.innerHTML;
                    }
                    button.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>`;
                } else {
                    button.disabled = false;
                    button.innerHTML = button.dataset.originalHtml || defaultHTML || 'Action';
                    delete button.dataset.originalHtml;
                }
            }



// --- Notification System ---

// --- Auto-Pilot Mode Logic ---
async function updateAutoBuyConfigJS(enabled) {
    const toggleSwitch = document.getElementById('auto-buy-toggle');
    if (!toggleSwitch) {
        console.warn('updateAutoBuyConfigJS: auto-buy-toggle not found.');
        return;
    }

    const originalState = toggleSwitch.checked;

    try {
        const response = await fetch('/api/auto_buy_config', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ enabled: enabled }),
        });
        if (!response.ok) throw await handleFetchError(response, "update Auto Buy config (JS)");
        const data = await response.json();

        if (data.status === 'success' && typeof data.enabled === 'boolean') {
            window.configData.auto_buy_enabled = data.enabled;
            if (toggleSwitch.checked !== window.configData.auto_buy_enabled) {
                 toggleSwitch.checked = window.configData.auto_buy_enabled;
            }
        } else {
            throw new Error(data.message || 'Failed to update Auto Buy config (JS).');
        }
    } catch (error) {
        console.error('Error updating Auto Buy config (JS):', error);
        if (toggleSwitch.checked !== originalState) toggleSwitch.checked = originalState;
    } finally {
        const goAutoPilotActive = document.getElementById('goAutoPilotToggle')?.checked || false;
        if (toggleSwitch) toggleSwitch.disabled = goAutoPilotActive;
    }
}

async function updateBuyOnDipConfigJS(enabled) {
    const toggleSwitch = document.getElementById('toggleBuyOnDip');
    if (!toggleSwitch) {
        console.warn('updateBuyOnDipConfigJS: toggleBuyOnDip not found.');
        return;
    }

    const originalState = toggleSwitch.checked;

    try {
        const response = await fetch('/api/buy_on_dip_config', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ enabled: enabled }),
        });
        if (!response.ok) throw await handleFetchError(response, "update Buy on Dip config (JS)");
        const data = await response.json();

        if (data.status === 'success' && data.config && typeof data.config.buy_on_dip_enabled === 'boolean') {
            window.buyOnDipConfig.enabled = data.config.buy_on_dip_enabled;
            if (toggleSwitch.checked !== window.buyOnDipConfig.enabled) {
                toggleSwitch.checked = window.buyOnDipConfig.enabled;
            }
        } else {
            throw new Error(data.message || 'Failed to update Buy on Dip config (JS).');
        }
    } catch (error) {
        console.error('Error updating Buy on Dip config (JS):', error);
        if (toggleSwitch.checked !== originalState) toggleSwitch.checked = originalState;
    } finally {
        const goAutoPilotActive = document.getElementById('goAutoPilotToggle')?.checked || false;
        if (toggleSwitch) toggleSwitch.disabled = goAutoPilotActive;
    }
}

// --- Function to update Auto Sell Config ---
            async function updateAutoSellConfig(enabled) {
                const toggleSwitch = document.getElementById('auto-sell-toggle');
                const originalState = toggleSwitch ? toggleSwitch.checked : !enabled;

                try {
                    const response = await fetch('/api/dont_sell', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ auto_sell_enabled: enabled }),
                    });
                    if (!response.ok) throw await handleFetchError(response, "update Auto Sell config");
                    const data = await response.json();

                    if (data.status === 'success' && data.config) {
                        window.dontSellData.auto_sell_enabled = data.config.auto_sell_enabled;
                        if (toggleSwitch) {
                            toggleSwitch.checked = window.dontSellData.auto_sell_enabled;
                        }
                        showToast(`Auto Sell feature ${window.dontSellData.auto_sell_enabled ? 'enabled' : 'disabled'}.`, 'success');
                        console.log('Auto Sell config updated:', data.config);
                    } else {
                        throw new Error(data.message || 'Failed to update Auto Sell status.');
                    }
                    // Manage disabled state based on Auto-Pilot
                    if (toggleSwitch) {
                        const goAutoPilotActive = document.getElementById('goAutoPilotToggle')?.checked || false;
                        toggleSwitch.disabled = goAutoPilotActive;
                    }
                } catch (error) {
                    console.error('Error updating Auto Sell config:', error.message || error);
                    showToast(`Error updating Auto Sell: ${error.message || 'Check console.'}`, 'error');
                    if (toggleSwitch) {
                        toggleSwitch.checked = originalState; 
                        const goAutoPilotActive = document.getElementById('goAutoPilotToggle')?.checked || false;
                        toggleSwitch.disabled = goAutoPilotActive;
                    }
                }
            }

            async function initializeGoAutoPilotMode() {
    const goAutoPilotToggle = document.getElementById('goAutoPilotToggle');
    const autoBuyToggle = document.getElementById('auto-buy-toggle');
    const autoSellToggle = document.getElementById('auto-sell-toggle');
    const buyOnDipToggle = document.getElementById('toggleBuyOnDip');

    if (!goAutoPilotToggle || !autoBuyToggle || !autoSellToggle || !buyOnDipToggle) {
        console.warn('Auto-Pilot mode: One or more required toggle switches not found. Aborting initialization.');
        if(!goAutoPilotToggle) console.error('Missing: goAutoPilotToggle');
        if(!autoBuyToggle) console.error('Missing: autoBuyToggle');
        if(!autoSellToggle) console.error('Missing: autoSellToggle');
        if(!buyOnDipToggle) console.error('Missing: buyOnDipToggle');
        return;
    }

    const childTogglesConfig = {
        autoBuy: { el: autoBuyToggle, id: 'autoBuyToggle', updateFn: updateAutoBuyConfigJS, storageKey: 'preAutoPilot_autoBuyToggleState' },
        autoSell: { el: autoSellToggle, id: 'autoSellToggle', updateFn: updateAutoSellConfig, storageKey: 'preAutoPilot_autoSellToggleState' },
        buyOnDip: { el: buyOnDipToggle, id: 'toggleBuyOnDip', updateFn: updateBuyOnDipConfigJS, storageKey: 'preAutoPilot_toggleBuyOnDipState' }
    };

    function setChildTogglesDisabled(disabled) {
        Object.values(childTogglesConfig).forEach(config => config.el.disabled = disabled);
    }

    async function applyAutoPilotStateChange(isAutoPilotNow) {
        goAutoPilotToggle.disabled = true;
        let apiCallSuccessful = false;

        try {
            const response = await fetch('/set_auto_pilot_status', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ auto_pilot_enabled: isAutoPilotNow }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                showToast(`Error setting Auto-Pilot: ${errorData.error || response.statusText}. Reverting.`, 'error', 5000);
                goAutoPilotToggle.checked = !isAutoPilotNow; // Revert toggle
                localStorage.setItem('goAutoPilotActiveFallback', !isAutoPilotNow);
            } else {
                localStorage.setItem('goAutoPilotActiveFallback', isAutoPilotNow);
                apiCallSuccessful = true;
            }
        } catch (error) {
            showToast('Network error setting Auto-Pilot. Reverting.', 'error', 5000);
            console.error('API error setting Auto-Pilot status:', error);
            goAutoPilotToggle.checked = !isAutoPilotNow; // Revert toggle
            localStorage.setItem('goAutoPilotActiveFallback', !isAutoPilotNow);
        }

        if (apiCallSuccessful) {
            if (isAutoPilotNow) {
                Object.values(childTogglesConfig).forEach(config => {
                    localStorage.setItem(config.storageKey, config.el.checked);
                });

                for (const key in childTogglesConfig) {
                    if (Object.prototype.hasOwnProperty.call(childTogglesConfig, key)) {
                        const config = childTogglesConfig[key];
                        if (!config.el.checked) {
                            config.el.checked = true;
                            try {
                                await config.updateFn(true);
                            } catch (error) {
                                console.error(`Failed to update ${config.id} during Auto-Pilot activation:`, error);
                                showToast(`Error enabling ${config.id.replace('Toggle', '')}. Check console.`, 'error', 5000);
                            }
                        }
                    }
                }
                setChildTogglesDisabled(true);
                showToast('Auto-Pilot mode ACTIVATED. All auto-features enabled.', 'success', 3000);
            } else { // isAutoPilotNow is false
                for (const key in childTogglesConfig) {
                    if (Object.prototype.hasOwnProperty.call(childTogglesConfig, key)) {
                        const config = childTogglesConfig[key];
                        const preState = localStorage.getItem(config.storageKey) === 'true';
                        if (config.el.checked !== preState) {
                            config.el.checked = preState;
                            try {
                                await config.updateFn(preState);
                            } catch (error) {
                                console.error(`Failed to update ${config.id} during Auto-Pilot deactivation:`, error);
                                showToast(`Error restoring ${config.id.replace('Toggle', '')}. Check console.`, 'error', 5000);
                            }
                        }
                    }
                }
                setChildTogglesDisabled(false);
                showToast('Auto-Pilot mode DEACTIVATED. Features restored to previous settings.', 'info', 3000);
            }
        } else {
            // If API call failed, the master toggle was reverted. Child toggles' visual state
            // should align with the reverted master toggle state.
            const revertedMasterState = goAutoPilotToggle.checked;
            if (revertedMasterState) { // Master toggle is ON (implies failed attempt to turn OFF)
                 Object.values(childTogglesConfig).forEach(config => config.el.checked = true);
                 setChildTogglesDisabled(true);
            } else { // Master toggle is OFF (implies failed attempt to turn ON)
                 Object.values(childTogglesConfig).forEach(config => {
                    const preState = localStorage.getItem(config.storageKey) === 'true';
                    config.el.checked = preState;
                 });
                 setChildTogglesDisabled(false);
            }
        }
        goAutoPilotToggle.disabled = false;
    }

    let initialAutoPilotState = false; // Default to false

    // Fetch initial state from API
    try {
        const response = await fetch('/get_auto_pilot_status');
        if (!response.ok) {
            const errorData = await response.json();
            showToast(`Error fetching Auto-Pilot status: ${errorData.error || response.statusText}. Using fallback.`, 'error', 5000);
            initialAutoPilotState = localStorage.getItem('goAutoPilotActiveFallback') === 'true';
        } else {
            const data = await response.json();
            initialAutoPilotState = data.auto_pilot_enabled;
            localStorage.setItem('goAutoPilotActiveFallback', initialAutoPilotState); // Store as new fallback
        }
    } catch (error) {
        showToast('Network error fetching Auto-Pilot status. Using fallback.', 'error', 5000);
        initialAutoPilotState = localStorage.getItem('goAutoPilotActiveFallback') === 'true';
        console.error('Error fetching Auto-Pilot status:', error);
    }

    goAutoPilotToggle.checked = initialAutoPilotState;

    // UI updates based on the fetched/fallback state
    if (initialAutoPilotState) {
        Object.values(childTogglesConfig).forEach(config => {
            config.el.checked = true;
        });
        setChildTogglesDisabled(true);
    } else {
        setChildTogglesDisabled(false);
    }

    goAutoPilotToggle.addEventListener('change', async function() {
        await applyAutoPilotStateChange(this.checked);
    });
}
// --- End Auto-Pilot Mode Logic ---

            // --- Init ---
            initializeDashboard();
            initializeGoAutoPilotMode(); // Initialize Auto-Pilot Mode
            setInterval(updateCashDisplay, 30000);
            setInterval(updateTrailingLogs, 5000);

            // --- Configuration Drawer Logic (Revised with Logging) ---
            const configDrawer = document.getElementById('configDrawer');
            const configDrawerButton = document.getElementById('configDrawerButton');
            const bodyForDrawer = document.body;

            if (configDrawerButton && configDrawer) {
                // Ensure drawer is closed initially by removing 'open' class if present
                configDrawer.classList.remove('open');
                console.log('Config Drawer Initialized. Initial classList (should be closed):', configDrawer.classList.toString());

                configDrawerButton.addEventListener('click', function (event) {
    event.stopPropagation();
    // Toggle the drawer on button click
    configDrawer.classList.toggle('open');
    
    // Force style update for visibility
    if (configDrawer.classList.contains('open')) {
        configDrawer.style.left = '0';
        configDrawer.style.display = 'block';
        configDrawer.style.visibility = 'visible';
        console.log('Button Click: Drawer OPENED. Style right:', configDrawer.style.right);
        
        // Dispatch event to trigger config fetching
        document.dispatchEvent(new Event('drawerOpened'));
    } else {
        configDrawer.style.left = '-350px';
        console.log('Button Click: Drawer CLOSED. Style right:', configDrawer.style.right);
    }
    
    console.log('Button Click: Drawer toggled. ClassList:', configDrawer.classList.toString());
});

                bodyForDrawer.addEventListener('click', function (event) {
                    // If the click target is the button itself, or a child of the button, or inside the drawer, do nothing here.
                    // The button's own click listener (with stopPropagation) should handle button clicks.
                    if (event.target === configDrawerButton || configDrawerButton.contains(event.target) || configDrawer.contains(event.target)) {
                        // console.log('Body click ignored: click was on button or inside drawer.');
                        return;
                    }

                    // If the drawer is open and the click was truly outside, close it.
                    if (configDrawer.classList.contains('open')) {
                        configDrawer.classList.remove('open');
                        // Force style update for visibility
                        configDrawer.style.left = '-350px';
                        console.log(`Body Click (Outside): Drawer closed. ClassList: ${configDrawer.classList.toString()}`);
                        console.log('Body Click: Drawer CLOSED. Style right:', configDrawer.style.right);
                    }
                });
            }
            // --- End Configuration Drawer Logic (Revised with Logging) ---

             // --- Global Error Handling ---
             window.onerror = function(message, source, lineno, colno, error) {
                 console.error("Unhandled Error:", message, "at", source, lineno, colno, error);
                 if (message && message.toLowerCase() !== 'script error.') {
                     showToast(`CLIENT ERROR: ${message}`, 'error', 10000);
                 }
                 return true;
             };
             window.onunhandledrejection = function(event) {
                 console.error("Unhandled Promise Rejection:", event.reason);
                 const reason = event.reason instanceof Error ? event.reason.message : String(event.reason);
                 if (!reason?.includes("Login required") && !reason?.includes("The user aborted a request") && !reason?.includes("aborted")) {
                    showToast(`CLIENT ASYNC ERROR: ${reason}`, 'error', 10000);
                 }
             };

        });
    </script>
</body>
</html>