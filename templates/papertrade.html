<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paper Trades</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.5/dist/sweetalert2.min.css">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='Breakout.ico') }}">

    <style>
        /* --- Copied styles from index.html --- */
        :root {
            --primary-rgb: 99, 102, 241;
            --primary: #6366f1;
            --primary-hover: #4f46e5;
            --success: #22c55e;
            --success-hover: #16a34a;
            --warning: #f59e0b;
            --danger: #ef4444;
            --danger-hover: #dc2626;
            --info: #3abff8;
            --muted: #64748b;
            --light: #f8fafc;
            --dark: #0f172a;
            --body-bg: var(--light);
            --body-color: #334155; /* Default text */
            --card-bg: #ffffff;
            --card-border: #e2e8f0;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.03), 0 1px 2px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.08);
            --table-header-bg: #f1f5f9;
            --table-row-hover-bg: #f8fafc;
            --input-bg: #ffffff; /* Added for potential future inputs */
            --input-border: #cbd5e1; /* Added */
            --input-focus-border: var(--primary); /* Added */
            --input-focus-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), .25); /* Added */
            --skeleton-bg: #e2e8f0;
            --skeleton-animation: skeleton-loading 1.5s infinite ease-in-out;
            --border-radius: 0.75rem;
            --transition-speed: 0.2s;
            --transition-ease: ease-in-out;
        }

        [data-bs-theme="dark"] {
            --muted: #94a3b8;
            --body-bg: var(--dark);
            --body-color: #cbd5e1;
            --card-bg: #1e293b;
            --card-border: #334155;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.2);
            --card-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.25);
            --table-header-bg: #293548;
            --table-row-hover-bg: #243147;
            --input-bg: #293548; /* Added */
            --input-border: #475569; /* Added */
            --skeleton-bg: #334155;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--body-bg);
            color: var(--body-color);
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease);
            font-size: 0.95rem;
        }

        /* Cards */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease), background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
            margin-bottom: 1.5rem;
             /* overflow: hidden; Removed */
        }
        .card:hover {
            transform: translateY(-3px);
            box-shadow: var(--card-shadow-hover);
        }
         .card-header {
            background-color: var(--table-header-bg);
            border-bottom: 1px solid var(--card-border);
            padding: 0.85rem 1.25rem;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
             border-bottom-left-radius: 0; /* Important for card structure */
             border-bottom-right-radius: 0;
            transition: background-color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .card-header h6 { font-weight: 600; margin-bottom: 0; }
        .card-body.p-0 { /* Ensure no padding interferes with the container */
            padding: 0 !important;
        }

        .profit-positive, .profit { color: var(--success); }
        .profit-negative, .loss { color: var(--danger); }

        /* Tables */
         .common-table-container {
             overflow-x: auto; /* Ensures horizontal scrolling */
             border: 1px solid var(--card-border);
             border-radius: var(--border-radius); /* Apply radius to the container */
             margin: -1px; /* Offset border */
             transition: border-color var(--transition-speed) var(--transition-ease);
             border-top-left-radius: 0; /* Default for cards with headers */
             border-top-right-radius: 0;
        }
         /* Apply top radius only if the container is the first element (and card has no header) */
         .card:not(:has(.card-header)) .card-body > .common-table-container:first-child {
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
         }

        .common-table {
            width: 100%;
            min-width: 750px; /* Ensure table has minimum width */
            border-collapse: separate; /* Use separate for rounded corners within container */
            border-spacing: 0;
            font-size: 0.9rem;
        }
        .common-table thead th {
            background-color: var(--table-header-bg);
            border-bottom: 2px solid var(--card-border);
            color: var(--body-color);
            font-weight: 600;
            padding: 0.75rem 0.85rem;
            white-space: nowrap;
            vertical-align: middle;
            transition: background-color var(--transition-speed) var(--transition-ease);
            text-align: center;
            position: sticky; /* Make header sticky */
            top: 0; /* Stick to the top of the scrolling container */
            z-index: 1; /* Ensure header stays above table content */
            /* cursor: pointer; Optional: add if sorting needed */
            user-select: none;
        }
        .common-table tbody tr {
            transition: background-color var(--transition-speed) var(--transition-ease), opacity 0.3s var(--transition-ease);
            opacity: 1;
         }
        .common-table tbody tr:hover {
            background-color: var(--table-row-hover-bg);
        }
        .common-table td {
            padding: 0.75rem 0.85rem;
            vertical-align: middle;
            border-top: 1px solid var(--card-border);
            text-align: center;
            white-space: nowrap; /* Keep content on one line, rely on scroll */
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
         /* Rounded corners handled by container, ensure last row cells have bottom radius */
        .common-table tbody tr:last-child td:first-child { border-bottom-left-radius: var(--border-radius); }
        .common-table tbody tr:last-child td:last-child { border-bottom-right-radius: var(--border-radius); }

         /* Alignment overrides */
        .common-table th.col-symbol, .common-table td.col-symbol,
        .common-table td.col-symbol-link {
            text-align: left;
            padding-left: 1rem;
        }
         .common-table th:last-child, .common-table td:last-child {
            padding-right: 1rem;
        }
         .common-table td.actions-cell {
             white-space: nowrap; /* Ensure button stays on one line */
             min-width: 100px; /* Give button space */
             vertical-align: middle;
        }

        /* Stock Badge */
        .stock-badge {
            background: rgba(var(--primary-rgb), 0.1);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid rgba(var(--primary-rgb), 0.2);
            text-decoration: none;
            display: inline-block;
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .stock-badge:hover { background: rgba(var(--primary-rgb), 0.15); color: var(--primary-hover); }

        /* Icons */
        .header-icon { color: var(--primary); }

        /* Buttons */
        .btn {
            border-radius: 0.5rem; padding: 0.5rem 1rem; font-weight: 500;
            transition: all var(--transition-speed) var(--transition-ease);
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            display: inline-flex; align-items: center; justify-content: center; gap: 0.4rem;
            border: 1px solid transparent;
            line-height: 1.5; /* Prevent text shift on spinner */
            min-height: calc(1.5em + 1rem + 2px); /* Ensure consistent height */
        }
        .btn:hover { transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0,0,0,0.08); }
        .btn:disabled { opacity: 0.65; cursor: not-allowed; transform: none; box-shadow: none; }
        .btn i { line-height: 1; vertical-align: middle; } /* Vertically align icon */
        .btn span { vertical-align: middle; } /* Vertically align text */
        .btn-primary { background-color: var(--primary); border-color: var(--primary); color: white; }
        .btn-primary:hover { background-color: var(--primary-hover); border-color: var(--primary-hover); }
        .btn-danger { background-color: var(--danger); border-color: var(--danger); color: white; }
        .btn-danger:hover { background-color: var(--danger-hover); border-color: var(--danger-hover); }
        .btn-outline-secondary { border-color: var(--card-border); color: var(--muted); }
        .btn-outline-secondary:hover { background-color: var(--table-row-hover-bg); border-color: var(--card-border); color: var(--body-color); }
        .btn-sm { padding: 0.35rem 0.8rem; font-size: 0.875rem; gap: 0.3rem; min-height: calc(1.5em + 0.7rem + 2px); }

        /* Notifications */
        #notifications-container { position: fixed; top: 20px; right: 20px; z-index: 1050; width: 320px; }
        .order-result {
            padding: 1rem; border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative; margin-bottom: 1rem;
            border: 1px solid transparent;
            opacity: 0; animation: toast-in 0.5s cubic-bezier(0.215, 0.610, 0.355, 1.000) forwards;
            background-color: var(--card-bg); color: var(--body-color);
            transform: translateX(100%);
            transition: background-color var(--transition-speed) var(--transition-ease), color var(--transition-speed) var(--transition-ease), border-color var(--transition-speed) var(--transition-ease);
        }
        .order-result.success { border-left: 4px solid var(--success); }
        .order-result.error { border-left: 4px solid var(--danger); }
        .order-result .fas { margin-right: 0.5rem; }
        .notification-close {
            position: absolute; top: 8px; right: 8px;
            background: none; border: none; cursor: pointer;
            font-size: 1.2em; color: var(--muted); opacity: 0.7;
            transition: opacity var(--transition-speed) ease; padding: 0; line-height: 1;
        }
        .notification-close:hover { opacity: 1; }
        @keyframes toast-in {
            0% { opacity: 0; transform: translateX(100%); }
            100% { opacity: 1; transform: translateX(0); }
        }
        @keyframes toast-out {
            0% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }

        /* Spinner */
         .spinner-border-sm { width: 1em; height: 1em; border-width: 0.15em; }
         .btn .spinner-border { display: inline-block; vertical-align: middle; }

        /* Skeleton Loading */
        .skeleton {
            background-color: var(--skeleton-bg);
            border-radius: 0.25rem;
            position: relative;
            overflow: hidden;
            display: block;
            width: 100%;
            min-height: 1em;
            transition: background-color var(--transition-speed) var(--transition-ease);
        }
        .skeleton::before {
            content: ''; position: absolute; top: 0; left: -150%; width: 150%; height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: var(--skeleton-animation);
        }
        [data-bs-theme="dark"] .skeleton::before {
             background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
        }
        @keyframes skeleton-loading {
            0% { left: -150%; }
            100% { left: 150%; }
        }
        .skeleton-text { height: 1em; margin-bottom: 0.5em; }
        .skeleton-text-short { width: 60%; height: 1em; margin-bottom: 0.5em; }
        .skeleton-badge { width: 80px; height: 24px; display: inline-block; border-radius: 6px; vertical-align: middle; }
        .skeleton-btn { width: 70px; height: calc(1.5em + 0.7rem + 2px); display: inline-block; border-radius: 0.5rem; vertical-align: middle; }

        /* Content Fade In */
        .content-fade-in {
            opacity: 0;
            animation: fadeInAnimation 0.5s ease-in-out forwards;
        }
        @keyframes fadeInAnimation {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive */
         @media (max-width: 992px) {
             .top-nav { flex-direction: column; align-items: flex-start !important; }
             .top-nav > div:last-child { margin-top: 0.5rem; align-self: flex-start; }
             .common-table { min-width: 700px; }
        }
        @media (max-width: 768px) {
            body { font-size: 0.9rem; }
            .container-xl { padding-left: 0.75rem; padding-right: 0.75rem; }
            /* Keep button text visible unless absolutely necessary */
            /* .btn span { display: none; } */ /* Ensure this is commented/removed */
            /* .btn { padding: 0.5rem 0.75rem; } */ /* Ensure this is commented/removed */
            /* .btn i { margin-right: 0 !important; } */ /* Ensure this is commented/removed */
            .common-table { min-width: 600px; } /* Adjust min-width */
            .common-table th, .common-table td { padding: 0.6rem 0.5rem; font-size: 0.85rem;}
             .common-table td.col-symbol-link, .common-table th.col-symbol { padding-left: 0.75rem;}
             .common-table td:last-child, .common-table th:last-child { padding-right: 0.75rem;}
             .btn-sm span.d-none.d-md-inline { display: none !important; } /* Explicitly hide span marked for md and up */
        }
         @media (max-width: 576px) {
             .common-table { min-width: 500px; } /* Further reduce */
         }

    </style>
</head>

<body>
    <div class="container-xl py-4">
        <!-- Header -->
        <div class="d-flex flex-column flex-lg-row justify-content-between align-items-lg-center gap-3 mb-4 top-nav">
            <div>
                 <img src="https://shoonya.com/static/img/shoonya_logo_full.a990668.webp" alt="Shoonya Logo"
                    style="height: 30px;">
            </div>
            <div class="d-flex gap-2 align-items-center">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i><span>Dashboard</span>
                </a>
                <a href="/trades" class="btn btn-primary">
                    <i class="fas fa-history"></i><span>Trade History</span>
                </a>
                <button id="theme-toggle" class="btn btn-outline-secondary">
                    <i id="theme-icon" class="fas fa-sun"></i>
                </button>
            </div>
        </div>

        <!-- Paper Trade Table -->
        <div class="card">
             <div class="card-header">
                <h6><i class="fas fa-file-alt me-2 header-icon"></i> Paper Trade Portfolio</h6>
            </div>
            <div class="card-body p-0"> <!-- Added p-0 here -->
                <div class="common-table-container"> <!-- This container handles scrolling -->
                    <table class="common-table" id="paper-trade-table">
                        <thead>
                            <tr>
                                <th class="col-symbol">Symbol</th>
                                <th>Quantity</th>
                                <th>Buy Price</th>
                                <th>Current Price</th>
                                <th>Total Invested</th>
                                <th>P/L Amount</th>
                                <th>P/L %</th>
                                <th>Sell</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if paper_trades %}
                                {% for trade in paper_trades %}
                                <tr class="content-fade-in" style="animation-delay: {{ loop.index0 * 0.05 }}s">
                                    <td class="col-symbol-link">
                                         <a href="https://www.tradingview.com/chart/?symbol=NSE%3A{{ trade.symbol }}"
                                            target="_blank" class="stock-badge">
                                            {{ trade.symbol }}
                                        </a>
                                    </td>
                                    <td>{{ trade.quantity }}</td>
                                    <td>₹{{ trade.price|round(2) }}</td>
                                    <td>₹{{ trade.current_price|round(2) }}</td>
                                    <td>₹{{ (trade.price * trade.quantity)|round(2) }}</td>
                                    {% set pl_amount = ((trade.current_price - trade.price) * trade.quantity)|round(2) %}
                                    <td class="{{ 'profit' if pl_amount >= 0 else 'loss' }}">
                                        ₹{{ pl_amount }}
                                    </td>
                                    {% set pl_percentage = (((trade.current_price - trade.price) / trade.price) * 100)|round(2) if trade.price != 0 else 0 %}
                                    <td class="{{ 'profit' if pl_percentage >= 0 else 'loss' }}">
                                        {{ pl_percentage }}%
                                    </td>
                                    <td class="actions-cell">
                                         <button class="btn btn-sm btn-danger sell-papertrade-btn"
                                            data-symbol="{{ trade.symbol }}">
                                            <i class="fas fa-dollar-sign"></i>
                                            <span>Sell</span> <!-- Keep text visible -->
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                             {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">No active paper trades found.</td>
                                </tr>
                            {% endif %}
                            <!-- Optional Skeleton Row (if loading via JS instead of server-side) -->
                             <!--
                             <tr class="skeleton-row">
                                 <td class="col-symbol-link"><span class="skeleton skeleton-badge"></span></td>
                                 <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                 <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                 <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                 <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                 <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                 <td><span class="skeleton skeleton-text-short mx-auto"></span></td>
                                 <td class="actions-cell"><span class="skeleton skeleton-btn mx-auto"></span></td>
                             </tr>
                             -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div> <!-- /container -->

    <!-- Notification Container -->
    <div id="notifications-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.5/dist/sweetalert2.all.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- Theme Toggle ---
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = document.getElementById('theme-icon');
            let isDarkMode = localStorage.getItem('theme') === 'dark' ||
                             (localStorage.getItem('theme') === null && window.matchMedia('(prefers-color-scheme: dark)').matches);

            const updateTheme = (dark) => {
                isDarkMode = dark;
                const newTheme = isDarkMode ? 'dark' : 'light';
                document.documentElement.setAttribute('data-bs-theme', newTheme);
                themeIcon.className = `fas fa-${isDarkMode ? 'moon' : 'sun'}`;
                const span = themeToggle.querySelector('span.d-none.d-lg-inline');
                if(span) span.textContent = isDarkMode ? 'Light' : 'Dark'; // Changed 'Dark' to 'Light' for consistency
                localStorage.setItem('theme', newTheme);
                // Update SweetAlert theme
                document.querySelectorAll('.swal2-popup').forEach(popup => {
                    popup.classList.remove('swal2-dark', 'swal2-light');
                    popup.classList.add(`swal2-${newTheme}`);
                 });
            };
            themeToggle.addEventListener('click', () => updateTheme(!isDarkMode));
            updateTheme(isDarkMode); // Init

            // --- Notifications ---
             const notificationsContainer = document.getElementById('notifications-container');
             function showToast(message, type = 'success', duration = 5000) {
                const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-times-circle';
                const toast = document.createElement('div');
                toast.className = `order-result ${type}`; // Animation applied via CSS
                toast.innerHTML = `
                    <button type="button" class="notification-close" aria-label="Close">×</button>
                    <i class="fas ${iconClass}"></i> ${message}
                `;
                notificationsContainer.appendChild(toast);

                 const timer = setTimeout(() => {
                     toast.style.animation = 'toast-out 0.4s ease-out forwards';
                     toast.addEventListener('animationend', () => toast.remove(), { once: true });
                }, duration);

                 toast.querySelector('.notification-close').addEventListener('click', (e) => {
                    e.stopPropagation();
                    clearTimeout(timer);
                     toast.style.animation = 'toast-out 0.4s ease-out forwards';
                     toast.addEventListener('animationend', () => toast.remove(), { once: true });
                });
            }

            // --- Utility: Set Button Loading State ---
            function setButtonLoading(button, isLoading, defaultHTML = null) {
                if (!button) return;
                if (isLoading) {
                    button.disabled = true;
                    // Store original content only if not already stored
                    if (!button.dataset.originalHtml) {
                        button.dataset.originalHtml = button.innerHTML;
                    }
                    // Set loading spinner, ensure it's visually centered
                    button.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>`;
                } else {
                    button.disabled = false;
                    // Restore original content if it was stored, otherwise use provided default or fallback
                    button.innerHTML = defaultHTML || button.dataset.originalHtml || 'Button';
                    // Clear stored content after restoring
                    delete button.dataset.originalHtml;
                }
            }


            // --- Sell Paper Trade ---
            document.querySelectorAll('.sell-papertrade-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const btn = this;
                    const symbol = btn.dataset.symbol;
                    const originalBtnHTML = btn.innerHTML; // Capture HTML before Swal

                    Swal.fire({
                        title: `Sell ${symbol} (Paper)?`,
                        text: "Confirm selling this paper trade position.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: 'var(--danger)',
                        cancelButtonColor: 'var(--muted)',
                        confirmButtonText: 'Yes, Sell Paper Trade',
                        customClass: {
                            popup: `swal2-${document.documentElement.getAttribute('data-bs-theme') || 'light'}`
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                             setButtonLoading(btn, true); // Use updated function
                             fetch('/api/papertrade/sell', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ 'symbol': symbol })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.status === 'success') {
                                    showToast(data.message || `Successfully sold paper trade ${symbol}`, 'success');
                                    const row = btn.closest('tr');
                                    if (row) {
                                        row.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
                                        row.style.opacity = '0';
                                        row.style.transform = 'translateX(-20px)';
                                        setTimeout(() => {
                                            row.remove();
                                            const tbody = document.querySelector('#paper-trade-table tbody');
                                            if (tbody && tbody.children.length === 0) {
                                                tbody.innerHTML = `<tr><td colspan="8" class="text-center text-muted py-4">No active paper trades found.</td></tr>`;
                                            }
                                        }, 400);
                                    } else {
                                         location.reload();
                                    }
                                } else {
                                    showToast(data.message || 'Error selling paper trade', 'error');
                                    setButtonLoading(btn, false, originalBtnHTML); // Restore on error
                                }
                            })
                            .catch(error => {
                                showToast(`Error: ${error.message}`, 'error');
                                setButtonLoading(btn, false, originalBtnHTML); // Restore on fetch error
                            });
                        }
                        // No else needed for cancel
                    });
                });
            });
        });
    </script>
</body>
</html>