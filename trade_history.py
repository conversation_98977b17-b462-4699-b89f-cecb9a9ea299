import json
import os
from datetime import datetime, time
from order_management import getCurrentPriceBySymbolName, getSymbolNameFinvasia

TRADE_HISTORY_FILE = os.path.join(os.path.dirname(__file__), 'json/trade_history.json')

def load_trade_history():
    try:
        if not os.path.exists(TRADE_HISTORY_FILE):
            return []
        with open(TRADE_HISTORY_FILE, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        print(f"Error loading trade history: {e}")
        return []

def save_trade_history(history):
    try:
        os.makedirs(os.path.dirname(TRADE_HISTORY_FILE), exist_ok=True)
        with open(TRADE_HISTORY_FILE, 'w') as f:
            json.dump(history, f, indent=2)
    except IOError as e:
        print(f"Error saving trade history: {e}")

def get_base_symbol(symbol):
    if isinstance(symbol, str):
        return symbol.split("-")[0]
    return symbol

def update_trade_history_buy(symbol, name, quantity, price, context="Unknown Buy"):
    history = load_trade_history()
    base_symbol = get_base_symbol(symbol)
    now = datetime.now().isoformat()
    existing_entry = next(
        (item for item in history if isinstance(item, dict) and get_base_symbol(item.get('symbol')) == base_symbol and item.get('sell_price') is None),
        None
    )

    if existing_entry:
        total_quantity = existing_entry.get('total_quantity', 0) + quantity
        old_total_value = (existing_entry.get('buy_price_avg', 0) * existing_entry.get('total_quantity', 0))
        new_added_value = (price * quantity)
        new_avg = (old_total_value + new_added_value) / total_quantity if total_quantity > 0 else 0

        existing_entry.update({
            'total_quantity': total_quantity,
            'buy_price_avg': round(new_avg, 2),
            'sell_price': None,
            'sell_date': None
        })
        print(f"Updated trade history for {symbol} (existing): New Avg Price = {round(new_avg, 2)}, Total Qty = {total_quantity}")
    else:
        history.append({
            'symbol': symbol,
            'name': name,
            'total_quantity': quantity,
            'buy_price_avg': round(price, 2),
            'buy_date': now,
            'sell_price': None,
            'sell_date': None,
            'current_price': None,
            'last_day_price': None,
            'buy_context': context
        })
        print(f"Added new trade history entry for {symbol}: Qty = {quantity}, Price = {round(price, 2)}")

    save_trade_history(history)

def update_trade_history_sell(symbol, quantity_sold, sell_price, sell_context="Manual Sell"):
    history = load_trade_history()
    now = datetime.now().isoformat()
    quantity_to_account_for = quantity_sold
    updated_history = []
    found_open_trade = False

    # Sort by buy_date to ensure FIFO if multiple open lots exist (though current logic consolidates buys)
    # For this refactor, we assume 'symbol' uniquely identifies an open position block in history
    # or that we process the first available open block.

    for item in history:
        if isinstance(item, dict) and item.get('symbol') == symbol and item.get('sell_price') is None and quantity_to_account_for > 0:
            found_open_trade = True
            current_lot_quantity = item.get('total_quantity', 0)

            if current_lot_quantity <= quantity_to_account_for:
                # This lot is fully sold or more than fully sold (if quantity_sold > current_lot_quantity)
                item.update({
                    'sell_price': round(float(sell_price), 2),
                    'sell_date': now,
                    'sell_context': sell_context,
                    'current_price': None, # Clear current price as it's sold
                    'last_day_price': None # Clear last day price as it's sold
                    # 'sold_quantity': current_lot_quantity # Optionally track sold quantity from this lot
                })
                updated_history.append(item)
                quantity_to_account_for -= current_lot_quantity
                print(f"Marked {current_lot_quantity} of {symbol} as sold at {float(sell_price):.2f}")

            else:
                # This lot is partially sold
                remaining_quantity = current_lot_quantity - quantity_to_account_for

                # Create a new entry for the sold portion
                sold_portion_entry = item.copy() # Start with a copy of the original buy
                sold_portion_entry.update({
                    'total_quantity': quantity_to_account_for, # Quantity that was sold from this lot
                    'sell_price': round(float(sell_price), 2),
                    'sell_date': now,
                    'sell_context': sell_context,
                    'current_price': None,
                    'last_day_price': None
                })
                updated_history.append(sold_portion_entry)
                print(f"Marked {quantity_to_account_for} of {symbol} (partial) as sold at {float(sell_price):.2f}")

                # Update the original entry for the remaining quantity
                item['total_quantity'] = remaining_quantity
                # buy_price_avg, buy_date, name, symbol remain the same for the open portion
                updated_history.append(item)
                print(f"{remaining_quantity} of {symbol} remains open after partial sell.")
                quantity_to_account_for = 0 # All sold quantity accounted for
        else:
            updated_history.append(item) # Keep other history items as they are

    if not found_open_trade:
        print(f"Warning: Could not find an open trade history entry for symbol {symbol} to mark as sold.")
    elif quantity_to_account_for > 0:
        print(f"Warning: Sold quantity ({quantity_sold}) for {symbol} exceeds available open quantity. {quantity_to_account_for} remains unaccounted for.")

    save_trade_history(updated_history)

    if found_open_trade and quantity_to_account_for == 0:
        print(f"Trade history successfully updated for selling {quantity_sold} of {symbol}.")
        from buy_on_dip import remove_symbol_from_buy_on_dip_state
        remove_symbol_from_buy_on_dip_state(symbol) # Clear buy on dip state if any part was sold
    elif found_open_trade and quantity_to_account_for > 0:
        # Still clear buy_on_dip if we attempted to sell, even if quantity mismatch
        from buy_on_dip import remove_symbol_from_buy_on_dip_state
        remove_symbol_from_buy_on_dip_state(symbol)

def update_last_day_price_in_history(api):
    print("Attempting to update last day prices in trade history...")
    history = load_trade_history()
    updated_count = 0
    for entry in history:
        if isinstance(entry, dict) and entry.get('sell_price') is None and entry.get('symbol'):
            trading_symbol = entry['symbol']
            try:
                current_price_str = getCurrentPriceBySymbolName(api, trading_symbol)
                if current_price_str is not None:
                    entry['last_day_price'] = round(float(current_price_str), 2)
                    updated_count += 1
                else:
                    entry['last_day_price'] = None
            except Exception as e:
                print(f"Error fetching price for {trading_symbol} for last day price update: {e}")
                entry['last_day_price'] = None
        elif isinstance(entry, dict) and entry.get('sell_price') is not None:
             if entry.get('last_day_price') is not None:
                 entry['last_day_price'] = None


    if updated_count > 0 or any(isinstance(entry, dict) and entry.get('sell_price') is None for entry in history):
        save_trade_history(history)
        print(f"Last day prices update finished. Updated {updated_count} entries.")
    else:
        print("Last day prices update finished. No open positions found.")