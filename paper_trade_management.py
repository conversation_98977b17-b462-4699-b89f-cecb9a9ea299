# paper_trade_management.py
import json
import os
from datetime import datetime
from order_management import getCurrentPriceBySymbolName  

PAPER_TRADE_FILE = os.path.join(os.path.dirname(__file__), 'json/paper_trades.json')

def load_paper_trades():
    if not os.path.exists(PAPER_TRADE_FILE):
        return []
    try:
        with open(PAPER_TRADE_FILE, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def save_paper_trades(trades):
    with open(PAPER_TRADE_FILE, 'w') as f:
        json.dump(trades, f, indent=4)

def add_paper_trade(symbol, name, quantity, price):
    paper_trades = load_paper_trades()
    existing_trade = next((trade for trade in paper_trades if trade['symbol'] == symbol), None)

    if existing_trade:
        # Update existing trade
        existing_trade['quantity'] += quantity
        # Recalculate price to be the average price.
        total_value = (existing_trade['price'] * (existing_trade['quantity'] - quantity)) + (price * quantity)
        existing_trade['price'] = round(total_value / existing_trade['quantity'], 2)
    else:
        # Create new trade
        paper_trades.append({
            'symbol': symbol,
            'name': name,
            'quantity': quantity,
            'price': round(price,2),
            'date': datetime.now().isoformat()
        })
    save_paper_trades(paper_trades)

def sell_paper_trade(symbol, api):
    paper_trades = load_paper_trades()
    trade_to_remove = next((trade for trade in paper_trades if trade['symbol'] == symbol), None)

    if trade_to_remove:
        current_price = getCurrentPriceBySymbolName(api, symbol)
        if current_price:  # Ensure current_price is not None
             profit = (float(current_price) - trade_to_remove['price']) * trade_to_remove['quantity']

             paper_trades.remove(trade_to_remove)
             save_paper_trades(paper_trades)
             return {'status': 'success', 'message': f"Sold {symbol} for a profit of {profit:.2f}"}
        else:
             return {'status': 'error', 'message': "Could not retrieve current price."}
    else:
        return {'status': 'error', 'message': f"No trade found for symbol {symbol}"}