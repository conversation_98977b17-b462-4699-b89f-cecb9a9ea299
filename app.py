from flask import Flask, render_template, jsonify, request
from flask_apscheduler import APScheduler
from fetch_and_buy_stock import fetch_stocks
from order_management import *
from datetime import datetime, time, date
import os, pyotp, json, sell_holding, logging, credentials as cr
from trade_history import update_trade_history_buy, load_trade_history, update_last_day_price_in_history, save_trade_history
import paper_trade_management as ptm
import concurrent.futures
from utils import load_config, save_config, update_auto_sell_status, CONFIG_FILE # Added CONFIG_FILE
from buy_on_dip import BUY_ON_DIP_STATE_FILE, load_buy_on_dip_state # Added for buy_on_dip_state
from buy_on_dip import execute_buy_on_dip_logic 
from NorenRestApiPy.NorenApi import NorenApi
import requests
from telegram_notifications import get_daily_summary_data, format_summary_message, send_telegram_message as send_tg_summary_message, send_telegram_message

app = Flask(__name__)

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# Initialize the scheduler
scheduler = APScheduler()
scheduler.init_app(app)
scheduler.start()

api = ShoonyaApiPy()

# Initialize login status
login_successful = False

def attempt_login():
    global login_successful
    try:
        if not all([cr.user, cr.pwd, cr.totp_token, cr.app_key]):
             logging.error("Credentials missing in credentials.py")
             login_successful = False
             return False
        totp_code = pyotp.TOTP(cr.totp_token).now()
        logging.info(f"Attempting login for user: {cr.user}")
        login_result = api.login(userid=cr.user, password=cr.pwd, twoFA=totp_code,
                                 vendor_code=cr.user+"_U", api_secret=cr.app_key, imei="ABC")

        if login_result and login_result.get('stat') == 'Ok':
            login_successful = True
            logging.info(f"Login successful for user: {cr.user}")
            # Clear trailing stop state on successful login
            sell_holding.clear_trailing_stop_state()
            logging.info("Cleared trailing stop state on login.")
            return True
        else:
            error_msg = login_result.get('emsg', 'Unknown login error') if login_result else 'Login failed'
            logging.error(f"Login failed: {error_msg}")
            login_successful = False
            return False
    except Exception as e:
        logging.error(f"Exception during login attempt: {e}", exc_info=True)
        login_successful = False
        return False

attempt_login()

# --- Daily Telegram Summary Job ---
def send_daily_telegram_summary():
    global login_successful, api 
    if not login_successful:
        logging.error("Telegram Summary: Login not successful. Skipping summary.")
        return

    logging.info("Generating daily Telegram summary...")
    try:
        # Pass the existing 'api' instance from app.py
        summary_data = get_daily_summary_data(api) 
        message = format_summary_message(summary_data)
        send_tg_summary_message(message)
        logging.info("Daily Telegram summary sent.")
    except Exception as e:
        logging.error(f"Error sending daily Telegram summary: {e}", exc_info=True)

if not scheduler.get_job('daily_telegram_summary'):
    scheduler.add_job(id='daily_telegram_summary', func=send_daily_telegram_summary, trigger='cron', hour=16, minute=5, day_of_week='mon-fri', timezone='Asia/Kolkata')
    logging.info("Scheduled daily Telegram summary job for 4:05 PM IST (Asia/Kolkata).")
else:
    logging.info("Daily Telegram summary job already scheduled.")

# --- Config API Routes ---
@app.route('/api/config/buy_on_dip', methods=['GET', 'POST'])
def manage_buy_on_dip_config():
    if request.method == 'GET':
        try:
            config = load_config()
            buy_on_dip_config = config.get('buy_on_dip_config', [])
            return jsonify({'buy_on_dip_config': buy_on_dip_config})
        except Exception as e:
            logging.error(f"Error loading buy_on_dip_config: {e}", exc_info=True)
            return jsonify({'error': 'Failed to load configuration'}), 500

    if request.method == 'POST':
        try:
            data = request.get_json()
            if data is None or 'buy_on_dip_config' not in data:
                return jsonify({'error': 'Missing buy_on_dip_config in request body'}), 400
            
            new_buy_on_dip_config = data['buy_on_dip_config']
            
            # Basic validation for the structure
            if not isinstance(new_buy_on_dip_config, list):
                return jsonify({'error': 'buy_on_dip_config must be a list'}), 400
            for item in new_buy_on_dip_config:
                if not (isinstance(item, dict) and 'threshold' in item and 'percentage' in item and \
                        isinstance(item['threshold'], (int, float)) and isinstance(item['percentage'], (int, float))):
                    return jsonify({'error': 'Invalid item format in buy_on_dip_config. Each item must be a dict with numeric threshold and percentage.'}), 400

            config = load_config() # Load current full config
            config['buy_on_dip_config'] = new_buy_on_dip_config # Update only the relevant part
            save_config(config) # Save the entire config back
            return jsonify({'message': 'Buy on Dip configuration updated successfully!'})
        except Exception as e:
            logging.error(f"Error updating buy_on_dip_config: {e}", exc_info=True)
            return jsonify({'error': 'Failed to update configuration'}), 500

stock_data = []
positions_symbols = set()
holdings_symbols = set()
purchased_stocks = set()
trailing_logs = []
trade_history_data = load_trade_history()

def log_trailing_message(message):
    global trailing_logs
    timestamp = datetime.now().isoformat()
    trailing_logs.append({"timestamp": timestamp, "message": message})
    if len(trailing_logs) > 100:
        trailing_logs.pop(0)

@app.route('/trades')
def trades():
    return render_template('trades.html')

def fetch_current_price(api_instance: ShoonyaApiPy, trading_symbol: str) -> float | None:
    """Helper function to fetch current price for a given trading symbol."""
    if not trading_symbol:
        return None
    try:
        current_price_str = getCurrentPriceBySymbolName(api_instance, trading_symbol)
        return round(float(current_price_str), 2) if current_price_str else None
    except Exception as e:
        logging.warning(f"Error getting current price for {trading_symbol}: {e}")
        return None

def _execute_buy(api_instance: ShoonyaApiPy, stocks_to_buy: list, amount: int | None = None, quantity: int | None = None, buy_as_paper_trade: bool = False, context: str = "Manual Buy") -> dict:
    """
    Internal function to execute buy orders (real or paper).
    Takes a list of stocks with 'symbol' (base symbol) and 'name'.
    Fetches current price and calculates quantity based on 'amount' OR uses fixed 'quantity'.
    Returns results including success/failure and purchased info.
    """
    global trade_history_data
    results = []
    new_purchases = set()
    purchased_info = []

    config = load_config()
    investment_amount = amount if amount is not None else config.get('investment_amount_per_stock', 500)

    for stock in stocks_to_buy:
        base_symbol = stock['symbol']
        stock_name = stock.get('name', base_symbol)

        # 1. Find the correct trading symbol (e.g., ABC-EQ)
        trading_symbol = getSymbolNameFinvasia(api_instance, base_symbol)
        if not trading_symbol:
            msg = f"Could not find trading symbol for {base_symbol}. Skipping buy."
            logging.warning(msg)
            results.append({'symbol': base_symbol, 'error': msg, 'quantity': 0, 'order_response': None})
            continue

        # 2. Fetch current price using the trading symbol
        current_price = fetch_current_price(api_instance, trading_symbol)
        if current_price is None:
            msg = f"Could not fetch current price for {base_symbol} ({trading_symbol}). Skipping buy."
            logging.warning(msg)
            results.append({'symbol': trading_symbol, 'error': msg, 'quantity': 0, 'order_response': None})
            continue

        # 3. Determine quantity
        qty_to_buy = 0
        if quantity is not None:
            qty_to_buy = int(quantity)
        elif investment_amount is not None and current_price > 0:
            qty_to_buy = int(investment_amount / current_price)
        else:
            msg = f"Invalid price ({current_price}) or missing amount/quantity for {trading_symbol}. Skipping buy."
            logging.warning(msg)
            results.append({'symbol': trading_symbol, 'error': msg, 'quantity': 0, 'order_response': None})
            continue

        if qty_to_buy < 1:
            msg = f"Calculated quantity is zero for {trading_symbol} (Price: {current_price}, Amount: {investment_amount}). Skipping buy."
            logging.info(msg)
            results.append({'symbol': trading_symbol, 'error': msg, 'quantity': 0, 'order_response': None})
            continue

        # 4. Execute Paper or Real Trade
        if buy_as_paper_trade:
            try:
                ptm.add_paper_trade(base_symbol, stock_name, qty_to_buy, current_price)
                logging.info(f"Paper trade successful: Bought {qty_to_buy} of {base_symbol} at {current_price:.2f}")
                results.append({
                    'symbol': trading_symbol,
                    'quantity': qty_to_buy,
                    'paper_trade': True,
                    'order_response': {'stat': 'Ok'}
                })
                purchased_info.append({'symbol': trading_symbol, 'quantity': qty_to_buy, 'price': current_price})
            except Exception as e:
                 logging.error(f"Error adding paper trade for {base_symbol}: {e}", exc_info=True)
                 results.append({'symbol': trading_symbol, 'quantity': qty_to_buy, 'paper_trade': True, 'error': str(e)})

        else:
            try:
                logging.info(f"Placing REAL BUY order: {qty_to_buy} of {trading_symbol} at price near {current_price:.2f}")
                order_response = placeOrder(api_instance, buy_or_sell='B', tradingsymbol=trading_symbol, quantity=qty_to_buy, price=current_price)

                if order_response and order_response.get('stat') == 'Ok':
                    logging.info(f"Real buy order successful for {trading_symbol}. Response: {order_response}")
                    results.append({
                        'symbol': trading_symbol,
                        'quantity': qty_to_buy,
                        'order_response': order_response
                    })
                    new_purchases.add(base_symbol)
                    purchased_info.append({'symbol': trading_symbol, 'quantity': qty_to_buy, 'price': current_price})

                    update_trade_history_buy(
                        symbol=trading_symbol,
                        name=stock_name,
                        quantity=qty_to_buy,
                        price=current_price,
                        context=context  # Pass the buy context
                    )
                    trade_history_data = load_trade_history()

                    # Send Telegram notification for successful real buy, if not manual
                    if context != "Manual Buy":
                        try:
                            message = f"✅ [{context}] BUY EXECUTED: {qty_to_buy} shares of {trading_symbol} at ~₹{current_price:.2f} each."
                            send_telegram_message(message)
                            logging.info(f"Telegram notification sent for {context} buy of {trading_symbol}.")
                        except Exception as e:
                            logging.error(f"Error sending Telegram notification for {context} buy of {trading_symbol}: {e}", exc_info=True)

                else:
                    error_msg = order_response.get('emsg', 'Unknown order error') if order_response else 'Order placement failed (no response)'
                    logging.error(f"Real buy order FAILED for {trading_symbol}: {error_msg}. Response: {order_response}")
                    results.append({
                        'symbol': trading_symbol,
                        'quantity': qty_to_buy,
                        'order_response': order_response,
                        'error': error_msg
                    })
            except Exception as e:
                 logging.error(f"Exception during real buy order for {trading_symbol}: {e}", exc_info=True)
                 results.append({'symbol': trading_symbol, 'quantity': qty_to_buy, 'error': str(e)})

    return {'results': results, 'new_purchases': list(new_purchases), 'purchased_info': purchased_info}
# --- End Refactored Buying Logic ---

@app.route('/api/trade_history')
def get_trade_history():
    global trade_history_data, login_successful
    # Use the globally loaded data
    current_trade_history = trade_history_data[:]

    if not login_successful:
        for trade in current_trade_history:
            trade['current_price'] = None
        return jsonify(current_trade_history)

    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        future_to_index = {
            executor.submit(fetch_current_price, api, trade['symbol']): idx
            for idx, trade in enumerate(current_trade_history) if trade.get('symbol')
        }
        for future in concurrent.futures.as_completed(future_to_index):
            index = future_to_index[future]
            original_symbol = current_trade_history[index]['symbol']
            try:
                current_price = future.result()
                current_trade_history[index]['current_price'] = current_price
            except Exception as e:
                logging.warning(f"Error fetching current price future for {original_symbol}: {e}")
                current_trade_history[index]['current_price'] = None

    return jsonify(current_trade_history)


# --- NEW ENDPOINT: Update Trade History Entry ---
@app.route('/api/trade_history/update', methods=['POST'])
def update_trade_history_entry():
    global login_successful, trade_history_data
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401

    data = request.json
    symbol = data.get('symbol')
    buy_date = data.get('buy_date')
    new_price_str = data.get('new_price')
    new_quantity_str = data.get('new_quantity')

    if not all([symbol, buy_date, new_price_str, new_quantity_str]):
        return jsonify({'error': 'Missing required fields (symbol, buy_date, new_price, new_quantity)'}), 400

    try:
        new_price = float(new_price_str)
        new_quantity = int(new_quantity_str)
        if new_price <= 0 or new_quantity <= 0:
            raise ValueError("Price and quantity must be positive")
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid number format for price or quantity'}), 400

    trade_found = False
    current_history = load_trade_history()

    for trade in current_history:
        if trade.get('symbol') == symbol and trade.get('buy_date') == buy_date:
            if trade.get('sell_price') is not None:
                return jsonify({'error': 'Cannot edit a closed trade'}), 400

            trade['buy_price_avg'] = round(new_price, 2)
            trade['total_quantity'] = new_quantity
            trade_found = True
            logging.info(f"Updated trade history for {symbol} (bought {buy_date}): Price={new_price}, Qty={new_quantity}")
            break

    if not trade_found:
        return jsonify({'error': 'Trade not found or could not be updated'}), 404

    save_trade_history(current_history)
    trade_history_data = current_history

    return jsonify({'status': 'success', 'message': f'Trade {symbol} updated successfully'})
# --- END NEW ENDPOINT ---


@app.route('/api/current_price')
def get_current_price_api():
    # Note: This endpoint expects the TRADING symbol (e.g., ABC-EQ)
    trading_symbol = request.args.get('symbol')
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401
    if not trading_symbol:
        return jsonify({'error': 'Trading symbol is required'}), 400

    current_price = fetch_current_price(api, trading_symbol)
    if current_price is not None:
        return jsonify({'symbol': trading_symbol, 'current_price': current_price})
    else:
        return jsonify({'error': f'Could not fetch current price for {trading_symbol}'}), 500

@app.route('/api/dont_sell', methods=['GET', 'POST'])
def handle_dont_sell():
    """Handles GET and POST requests for the dont_sell list and percentages."""
    config = load_config()

    if request.method == 'POST':
        data = request.json
        symbol = data.get('symbol')
        action = data.get('action')
        auto_sell_enabled_update = data.get('auto_sell_enabled') # Check for auto_sell_enabled update

        updated = False
        if symbol and action:
            dont_sell_list = config.get('symbols', [])
            if action == 'add' and symbol not in dont_sell_list:
                dont_sell_list.append(symbol)
                logging.info(f"Added {symbol} to Don't Sell list.")
                sell_holding.remove_symbol_from_trailing_stop(symbol)
                config['symbols'] = dont_sell_list
                updated = True
            elif action == 'remove' and symbol in dont_sell_list:
                dont_sell_list.remove(symbol)
                logging.info(f"Removed {symbol} from Don't Sell list.")
                config['symbols'] = dont_sell_list
                updated = True
        
        if auto_sell_enabled_update is not None and isinstance(auto_sell_enabled_update, bool):
            if config.get('auto_sell_enabled') != auto_sell_enabled_update:
                config['auto_sell_enabled'] = auto_sell_enabled_update
                logging.info(f"Auto sell enabled set to: {auto_sell_enabled_update}")
                updated = True

        if updated:
            save_config(config)
            return jsonify({'status': 'success', 'config': config}) # Return the whole updated config
        else:
            # If only symbol/action was provided but no change was made, or if only auto_sell_enabled was provided but it was the same
            # or if neither symbol/action nor auto_sell_enabled was provided correctly.
            return jsonify({'status': 'error', 'message': 'No valid update provided or no change made'}), 400

    # GET request returns the full config relevant to this section
    return jsonify({
        'symbols': config.get('symbols', []),
        'sell_percentage': config.get('sell_percentage', 2.0),
        'trailing_stop_loss_percentage': config.get('trailing_stop_loss_percentage', 0.5),
        'auto_sell_enabled': config.get('auto_sell_enabled', False) # Include auto_sell_enabled status
    })

# Separate endpoints for updating percentages for clarity
@app.route('/api/sell_percentage', methods=['POST'])
def update_sell_percentage():
    data = request.json
    try:
        percentage = float(data.get('percentage', 2.0))
        if percentage <= 0:
            return jsonify({'status': 'error', 'message': 'Percentage must be positive'}), 400
    except (ValueError, TypeError):
         return jsonify({'status': 'error', 'message': 'Invalid percentage value'}), 400

    config = load_config()
    config['sell_percentage'] = percentage
    save_config(config)
    sell_holding.clear_trailing_stop_state() # Clear trailing state when sell % changes
    logging.info(f"Sell trigger % updated to {percentage}. Trailing state cleared.")
    return jsonify({'status': 'success', 'sell_percentage': percentage})

@app.route('/api/trailing_sl_percentage', methods=['POST'])
def update_trailing_sl_percentage():
    data = request.json
    try:
        trailing_stop_loss_percentage = float(data.get('trailing_stop_loss_percentage', 0.5))
        if trailing_stop_loss_percentage <= 0:
            return jsonify({'status': 'error', 'message': 'Percentage must be positive'}), 400
    except (ValueError, TypeError):
        return jsonify({'status': 'error', 'message': 'Invalid percentage value'}), 400

    config = load_config()
    config['trailing_stop_loss_percentage'] = trailing_stop_loss_percentage
    save_config(config)
    sell_holding.clear_trailing_stop_state()
    logging.info(f"Trailing SL % updated to {trailing_stop_loss_percentage}. Trailing state cleared.")
    return jsonify({'status': 'success', 'trailing_stop_loss_percentage': trailing_stop_loss_percentage})

# --- API for Auto Buy Configuration ---
@app.route('/api/auto_buy_config', methods=['GET', 'POST'])
def auto_buy_config():
    """GET: Returns current auto-buy status. POST: Updates auto-buy status."""
    config = load_config()
    if request.method == 'POST':
        if not login_successful:
             return jsonify({'error': 'Login required to change config'}), 401
        data = request.json
        is_enabled = data.get('enabled')
        if isinstance(is_enabled, bool):
            config['auto_buy_enabled'] = is_enabled
            save_config(config)
            logging.info(f"Auto-buy feature {'enabled' if is_enabled else 'disabled'}")
            return jsonify({'status': 'success', 'enabled': is_enabled})
        else:
            return jsonify({'status': 'error', 'message': 'Invalid value for enabled status'}), 400
    else:
        return jsonify({'enabled': config.get('auto_buy_enabled', False)})
# --- End Auto Buy Config API ---

# --- API for Investment Amount ---
@app.route('/api/investment_amount', methods=['GET', 'POST'])
def investment_amount_config():
    """GET: Returns current investment amount. POST: Updates it."""
    config = load_config()
    if request.method == 'POST':
        if not login_successful:
             return jsonify({'error': 'Login required to change config'}), 401
        data = request.json
        amount = data.get('amount')
        try:
            amount_val = int(amount)
            if amount_val >= 500:
                 config['investment_amount_per_stock'] = amount_val
                 save_config(config)
                 logging.info(f"Investment amount per stock updated to {amount_val}")
                 return jsonify({'status': 'success', 'amount': amount_val})
            else:
                 return jsonify({'status': 'error', 'message': 'Amount must be at least 500'}), 400
        except (ValueError, TypeError):
             return jsonify({'status': 'error', 'message': 'Invalid amount value'}), 400
    else: # GET Request
        return jsonify({'amount': config.get('investment_amount_per_stock', 500)})
# --- End Investment Amount API ---

# --- API for Deposit Amount ---
@app.route('/api/deposit_amount', methods=['GET', 'POST'])
def deposit_amount_config():
    """GET: Returns current deposit amount. POST: Updates it."""
    config = load_config()
    if request.method == 'POST':
        data = request.json
        amount = data.get('amount')
        try:
            amount_val = float(amount)
            if amount_val >= 0:
                 config['deposit_amount'] = round(amount_val, 2)
                 save_config(config)
                 logging.info(f"Deposit amount updated to {amount_val:.2f}")
                 return jsonify({'status': 'success', 'amount': round(amount_val, 2)})
            else:
                 return jsonify({'status': 'error', 'message': 'Deposit amount cannot be negative'}), 400
        except (ValueError, TypeError):
             return jsonify({'status': 'error', 'message': 'Invalid amount value'}), 400
    else: # GET Request
        return jsonify({'amount': round(config.get('deposit_amount', 0.0), 2)})
# --- End Deposit Amount API ---

# --- API for Buy On Dip Configuration ---
@app.route('/api/buy_on_dip_config', methods=['GET', 'POST'])
def buy_on_dip_config_api():
    """GET: Returns current buy_on_dip config. POST: Updates it."""
    config = load_config()
    if request.method == 'POST':
        if not login_successful:
            return jsonify({'error': 'Login required to change config'}), 401
        data = request.json
        
        new_config_updates = {}
        if 'enabled' in data and isinstance(data['enabled'], bool):
            new_config_updates['buy_on_dip_enabled'] = data['enabled']
            logging.info(f"Buy on dip feature {'enabled' if data['enabled'] else 'disabled'}.")
        
        if 'buy_on_dip_config' in data and isinstance(data['buy_on_dip_config'], list):
            try:
                new_dip_config = []
                for item in data['buy_on_dip_config']:
                    if not isinstance(item, dict) or 'threshold' not in item or 'percentage' not in item:
                        raise ValueError("Each item in buy_on_dip_config must be an object with 'threshold' and 'percentage'.")
                    threshold = int(item['threshold'])
                    percentage = float(item['percentage'])
                    if not (0 < threshold <= 100 and 0 < percentage <= 100):
                        raise ValueError("Thresholds and percentages must be positive and reasonable (1-100).")
                    new_dip_config.append({'threshold': threshold, 'percentage': percentage})
                
                # Sort by threshold ascending for consistency in storage, though usage might sort differently
                new_config_updates['buy_on_dip_config'] = sorted(new_dip_config, key=lambda x: x['threshold'])
                logging.info(f"Buy on dip config updated to: {new_config_updates['buy_on_dip_config']}")
            except ValueError as e:
                return jsonify({'status': 'error', 'message': f'Invalid buy_on_dip_config value: {e}'}), 400
            except TypeError:
                 return jsonify({'status': 'error', 'message': 'Invalid type in buy_on_dip_config items.'}), 400

        if not new_config_updates:
            return jsonify({'status': 'error', 'message': 'No valid configuration data provided'}), 400

        for key, value in new_config_updates.items():
            config[key] = value
        save_config(config)
        
        # Return the relevant part of the config
        return jsonify({'status': 'success', 'config': {
            'buy_on_dip_enabled': config.get('buy_on_dip_enabled'),
            'buy_on_dip_config': config.get('buy_on_dip_config', [])
        }})

    else: # GET Request
        return jsonify({
            'buy_on_dip_enabled': config.get('buy_on_dip_enabled', False),
            'buy_on_dip_config': config.get('buy_on_dip_config', [])
        })
# --- End Buy On Dip Config API ---


def process_positions():
    """Fetches and processes current open positions."""
    global login_successful, trade_history_data, positions_symbols
    if not login_successful:
        return [], 0.0, 0.0, set()

    positions_list = []
    total_invested = 0.0
    total_unrealized = 0.0
    current_positions_symbols = set()

    try:
        position_response_app = api.get_positions()
        if position_response_app is None:
             logging.warning("API returned None for positions.")
             return positions_list, total_invested, total_unrealized, current_positions_symbols

        for position in position_response_app:
            if isinstance(position, dict) and position.get('stat') == 'Ok' and position.get('prd') == 'C':
                try:
                    trading_symbol = position['tsym']
                    base_symbol = trading_symbol.split('-')[0]
                    avg_price = round(float(position['daybuyavgprc']), 2)
                    qty = int(position['netqty'])
                    unrealized = round(float(position['urmtom']), 2)
                    invested = round(float(position.get('daybuyamt', 0.0)), 2)
                    if invested == 0.0 and avg_price > 0 and qty > 0:
                        invested = round(avg_price * qty, 2)

                    if avg_price == 0.0:
                        for trade in trade_history_data:
                            if isinstance(trade, dict) and trade.get('symbol') and trade['symbol'].split('-')[0] == base_symbol and trade.get('sell_price') is None:
                                avg_price = trade.get('buy_price_avg', 0.0)
                                if invested == 0.0 and avg_price > 0 and qty > 0:
                                    invested = round(avg_price * qty, 2)
                                break

                    current_price = fetch_current_price(api, trading_symbol)
                    current_price_val = round(current_price, 2) if current_price is not None else avg_price

                    position_data = {
                        'tsym': trading_symbol,
                        'avg_price': avg_price,
                        'quantity': qty,
                        'invested': invested,
                        'unrealized': unrealized,
                        'current_price': current_price_val
                    }
                    positions_list.append(position_data)
                    total_invested += invested
                    total_unrealized += unrealized
                    current_positions_symbols.add(base_symbol)

                except (ValueError, TypeError, KeyError) as e:
                     logging.error(f"Error processing position item: {e}. Data: {position}", exc_info=True)
                     continue

            elif isinstance(position, dict) and position.get('stat') == 'Not_Ok':
                logging.warning(f"Error status in position item: {position.get('emsg', 'Unknown error')}")
            elif not isinstance(position, dict):
                logging.warning(f"Received non-dictionary item in positions response: {position}")

    except Exception as e:
        logging.error(f"Exception fetching or processing positions: {e}", exc_info=True)

    positions_symbols = current_positions_symbols
    return positions_list, total_invested, total_unrealized, current_positions_symbols

def get_holdings_internal():
    """Fetches and processes current holdings, returns list and set of base symbols."""
    global login_successful, trade_history_data, holdings_symbols
    if not login_successful:
        return [], set()

    holdings_list = []
    current_holdings_symbols = set()

    try:
        holdings_response = api.get_holdings()
        if holdings_response is None or not isinstance(holdings_response, list):
            logging.warning(f"Received invalid holdings response: {holdings_response}")
            return holdings_list, current_holdings_symbols

        for holding in holdings_response:
            if isinstance(holding, dict) and holding.get('stat') == 'Ok':
                trading_symbol = None
                exch_tsym_list = holding.get('exch_tsym', [])
                if isinstance(exch_tsym_list, list):
                    for tsym_info in exch_tsym_list:
                        if isinstance(tsym_info, dict) and tsym_info.get('exch') == 'NSE':
                            trading_symbol = tsym_info.get('tsym')
                            break

                if trading_symbol:
                    try:
                        base_symbol = trading_symbol.split('-')[0]
                        qty = int(holding.get('holdqty', 0))
                        used_qty = int(holding.get('usedqty', 0))
                        avg_price = float(holding.get('upldprc', 0.0))

                        if qty == used_qty or qty == 0:
                            continue

                        if avg_price == 0.0:
                            for trade in trade_history_data:
                                if isinstance(trade, dict) and trade.get('symbol') and trade['symbol'].split('-')[0] == base_symbol and trade.get('sell_price') is None:
                                    avg_price = trade.get('buy_price_avg', 0.0)
                                    break

                        current_price = fetch_current_price(api, trading_symbol)
                        current_price_val = round(current_price, 2) if current_price is not None else avg_price

                        last_day_price = None
                        for trade in trade_history_data:
                             if isinstance(trade, dict) and trade.get('symbol') and trade['symbol'].split('-')[0] == base_symbol and trade.get('sell_price') is None:
                                last_day_price = trade.get('last_day_price')
                                break
                        last_day_price_rounded = round(last_day_price, 2) if last_day_price is not None else None


                        holdings_list.append({
                            'symbol': trading_symbol,
                            'quantity': qty,
                            'average_price': round(avg_price, 2),
                            'current_price': current_price_val,
                            'last_day_price': last_day_price_rounded
                        })
                        current_holdings_symbols.add(base_symbol)

                    except (ValueError, TypeError, KeyError) as e:
                         logging.error(f"Error processing holding item for {trading_symbol}: {e} - Data: {holding}", exc_info=True)
                         continue

            elif isinstance(holding, dict) and holding.get('stat') == 'Not_Ok':
                logging.warning(f"Error status in holding item: {holding.get('emsg', 'Unknown error')}")
            elif not isinstance(holding, dict):
                 logging.warning(f"Received non-dictionary item in holdings response: {holding}")

    except Exception as e:
        logging.error(f"Exception fetching or processing holdings: {e}", exc_info=True)

    holdings_symbols = current_holdings_symbols
    return holdings_list, current_holdings_symbols


@app.route('/')
def index():
    positions, total_invested, total_unrealized, _ = process_positions()
    holdings, _ = get_holdings_internal()
    config = load_config()

    return render_template('index.html',
                         login_successful=login_successful)


@app.route('/api/positions')
def get_positions_api():
    """API endpoint to get current positions."""
    try:
        positions, total_invested, total_unrealized, _ = process_positions()
        return jsonify({
            'positions': positions,
            'total_invested': round(total_invested, 2),
            'total_unrealized': round(total_unrealized, 2)
        })
    except Exception as e:
        logging.error(f"API Error fetching positions: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error fetching positions: {e}"}), 500

@app.route('/api/stocks')
def get_stocks_api():
    """API endpoint to get identified stocks."""
    global stock_data, positions_symbols
    try:
        fetch_stocks(stock_data, positions_symbols)

        if login_successful:
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                 future_to_stock = {
                     executor.submit(fetch_current_price, api, getSymbolNameFinvasia(api, stock['symbol'])): stock
                     for stock in stock_data if stock.get('symbol')
                 }
                 for future in concurrent.futures.as_completed(future_to_stock):
                    stock_item = future_to_stock[future]
                    try:
                        price = future.result()
                        stock_item['current_price'] = price
                    except Exception:
                        stock_item['current_price'] = None

        return jsonify(stock_data)

    except Exception as e:
        logging.error(f"API Error fetching stocks: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error fetching stocks: {e}"}), 500


@app.route('/api/limits')
def get_limits():
    global login_successful
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401
    try:
        limit_response = api.get_limits()

        if limit_response is None or limit_response.get('stat') != 'Ok':
             emsg = limit_response.get('emsg', 'Unknown error') if limit_response else 'No response'
             logging.error(f"Error fetching limits: {emsg}")
             return jsonify({'error': f"Failed to fetch limits: {emsg}"}), 500

        cash = float(limit_response.get('cash', 0.0))
        payin = float(limit_response.get('payin', 0.0))
        payout = float(limit_response.get('payout', 0.0))
        marginused = float(limit_response.get('marginused', 0.0))
        available_balance = cash + payin - payout - marginused

        return jsonify({'cash': round(available_balance, 2)})
    except Exception as e:
        logging.error(f"Error fetching limits: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error fetching limits: {e}"}), 500

@app.route('/buy_stocks', methods=['POST'])
def buy_stocks_api():
    """API endpoint for manually buying stocks from UI."""
    global login_successful
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401
    try:
        data = request.get_json()
        stocks_to_buy = data.get('stocks', [])
        amount = data.get('amount')
        quantity = data.get('quantity')
        buy_as_paper_trade = data.get('buy_as_paper_trade', False)

        if not stocks_to_buy:
             return jsonify({'error': 'No stocks provided to buy.'}), 400

        buy_result = _execute_buy(api, stocks_to_buy, amount=amount, quantity=quantity, buy_as_paper_trade=buy_as_paper_trade)

        if not buy_as_paper_trade and buy_result.get('new_purchases'):
             process_positions()
             get_holdings_internal()

        return jsonify({
            'results': buy_result.get('results', []),
            'purchased_info': buy_result.get('purchased_info', []),
            'message': 'Stock buy process completed.'
        })

    except Exception as e:
        logging.error(f"API Error buying stocks: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error processing buy request: {e}"}), 500

@app.route('/api/holdings')
def get_holdings_api():
    """API endpoint to get current holdings (full data)."""
    global login_successful
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401
    try:
        holdings, _ = get_holdings_internal()
        return jsonify({'holdings': holdings})
    except Exception as e:
        logging.error(f"API Error fetching holdings: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error fetching holdings: {e}"}), 500

@app.route('/api/holdings/current_prices', methods=['POST'])
def get_holdings_current_prices():
    """
    API endpoint to get current prices for a list of trading symbols.
    Also returns active trailing symbols.
    """
    global login_successful
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401

    data = request.json
    symbols_to_fetch = data.get('symbols', [])

    if not isinstance(symbols_to_fetch, list):
        return jsonify({'error': 'Invalid input format, "symbols" should be a list.'}), 400

    prices_map = {}
    active_trail_symbols = []

    if symbols_to_fetch:
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_symbol = {
                executor.submit(fetch_current_price, api, tsym): tsym
                for tsym in symbols_to_fetch if isinstance(tsym, str)
            }
            for future in concurrent.futures.as_completed(future_to_symbol):
                original_tsym = future_to_symbol[future]
                try:
                    current_price = future.result()
                    if current_price is not None:
                        prices_map[original_tsym] = current_price
                except Exception as e:
                    logging.warning(f"Error fetching current price for {original_tsym} in partial update: {e}")
                    prices_map[original_tsym] = None 

    try:
        trailing_state = sell_holding.load_trailing_stop_state()
        active_trail_symbols = [symbol for symbol, data in trailing_state.items() if data.get('activated', False)]
    except Exception as e:
        logging.error(f"Error fetching active trailing symbols for partial update: {e}")

    return jsonify({
        'prices': prices_map,
        'active_trail_symbols': active_trail_symbols
    })


def should_run_sell_holding():
    """Check if current time is within market hours (weekdays 9:15 AM - 3:30 PM IST)."""
    now_dt = datetime.now()
    # Market is open on weekdays (0=Monday, 4=Friday)
    if now_dt.weekday() < 5:
        market_open_time = time(9, 15)
        market_close_time = time(15, 30)
        return market_open_time <= now_dt.time() <= market_close_time
    return False

@scheduler.task('interval', id='sell_holding_job', seconds=10, misfire_grace_time=5)
def scheduled_sell_holding():
    global trailing_logs, login_successful, trade_history_data
    if not login_successful:
        if not (trailing_logs and trailing_logs[-1]['message'].startswith("Login required")):
            log_trailing_message("Login required for automated selling.")
        return

    if should_run_sell_holding():
        try:
            sell_holding.sell_holding(api, logger=log_trailing_message)
            trade_history_data = load_trade_history() 
            logging.debug("Reloaded trade_history_data after scheduled sell holding execution.")
        except Exception as e:
             logging.error(f"Exception in scheduled_sell_holding task: {e}", exc_info=True)
             log_trailing_message(f"Error during automated selling: {e}")
    else:
        # Add market closed message only if last log isn't already the same
        if not (trailing_logs and trailing_logs[-1]['message'] == "Market is closed..!!"):
            log_trailing_message("Market is closed..!!")

def scheduled_update_last_day_price():
    global login_successful, trade_history_data 
    if login_successful:
        logging.info("Running scheduled job to update last day prices in history.")
        try:
            update_last_day_price_in_history(api)
            trade_history_data = load_trade_history()
            logging.info("Finished updating last day prices and reloaded trade_history_data.")
        except Exception as e:
             logging.error(f"Exception in scheduled_update_last_day_price task: {e}", exc_info=True)
    else:
        logging.warning("Login not successful, skipping scheduled last day price update.")

# Schedule the last day price update job after market close
scheduler.add_job(
    id='update_last_day_price_job',
    func=scheduled_update_last_day_price,
    trigger='cron',
    hour='16',
    minute='5',
    day_of_week='mon-fri',
    timezone='Asia/Kolkata',
    misfire_grace_time=300 # 5 minutes grace time
)

# --- Scheduled job for Buy On Dip --- 
def scheduled_buy_on_dip_task():
    """Scheduled task to execute the buy on dip logic during market hours."""
    global login_successful, api
    if login_successful:
        config = load_config()
        if config.get('buy_on_dip_enabled', False):
            logging.info("Scheduler: Running buy on dip check...")
            try:
                execute_buy_on_dip_logic(api)
            except Exception as e:
                logging.error(f"Error during scheduled buy_on_dip_task: {e}", exc_info=True)
        else:
            logging.info("Scheduler: Buy on dip feature is disabled, skipping check.")
    else:
        logging.info("Scheduler: Buy on dip check skipped, user not logged in.")

# Schedule the buy on dip check every 2 minutes during market hours (Mon-Fri 9:10 AM - 3:40 PM IST)
if not scheduler.get_job('buy_on_dip_check_job'):
    scheduler.add_job(
        id='buy_on_dip_check_job', 
        func=scheduled_buy_on_dip_task, 
        trigger='cron', 
        minute='20-25/2,27-59/2,0-25/2',  # Every 2 minutes from 9:20 AM to 3:25 PM
        hour='9-15',  # 9 AM to 3 PM
        day_of_week='mon-fri',  # Monday to Friday
        timezone='Asia/Kolkata',
        misfire_grace_time=60
    )
    logging.info("Scheduled buy on dip job for Mon-Fri 9:20 AM - 3:25 PM IST every 2 minutes.")
else:
    logging.info("Buy on dip job already scheduled.")

# --- Auto Sell Toggle Scheduled Jobs ---

def schedule_enable_auto_sell():
    """Scheduled job to enable auto sell."""
    logging.info("Scheduler: Enabling auto sell holding.")
    try:
        update_auto_sell_status(True)
    except Exception as e:
        logging.error(f"Error during scheduled_enable_auto_sell: {e}", exc_info=True)

def schedule_disable_auto_sell():
    """Scheduled job to disable auto sell."""
    logging.info("Scheduler: Disabling auto sell holding.")
    try:
        update_auto_sell_status(False)
    except Exception as e:
        logging.error(f"Error during scheduled_disable_auto_sell: {e}", exc_info=True)

scheduler.add_job(
    id='enable_auto_sell_job',
    func=schedule_enable_auto_sell,
    trigger='cron',
    hour='9',
    minute='0',
    day_of_week='mon-fri',
    timezone='Asia/Kolkata',
    misfire_grace_time=300
)

scheduler.add_job(
    id='disable_auto_sell_job',
    func=schedule_disable_auto_sell,
    trigger='cron',
    hour='15',
    minute='35',
    day_of_week='mon-fri',
    timezone='Asia/Kolkata',
    misfire_grace_time=300
)

# --- Auto Buy Scheduled Job ---
@scheduler.task('cron', id='auto_buy_job', hour=15, minute=20, day_of_week='mon-fri', timezone='Asia/Kolkata', misfire_grace_time=120)
def auto_buy_identified_stocks():
    """Scheduled job to automatically buy identified stocks not already held or sold today."""
    global login_successful, stock_data, positions_symbols, holdings_symbols, trade_history_data

    logging.info("--- Running Auto-Buy Job (3:20 PM) ---")

    config = load_config()
    if not config.get('auto_buy_enabled', False):
        logging.info("Auto-buy feature is disabled in config. Skipping.")
        return

    if not login_successful:
        logging.warning("Auto-buy job: Login not successful. Skipping.")
        return

    try:
        # --- Fetch Sold Today Symbols ---
        today_str = date.today().strftime('%Y-%m-%d')
        sold_today_symbols = set()
        current_history_for_autobuy = trade_history_data
        for trade in current_history_for_autobuy:
            if isinstance(trade, dict):
                sell_date_str = trade.get('sell_date')
                if isinstance(sell_date_str, str) and sell_date_str.startswith(today_str):
                    trading_symbol = trade.get('symbol')
                    if trading_symbol and isinstance(trading_symbol, str):
                        base_symbol_sold = trading_symbol.split('-')[0]
                        sold_today_symbols.add(base_symbol_sold)
        if sold_today_symbols:
            logging.info(f"Auto-buy: Identified symbols sold today: {sold_today_symbols}")


        # 1. Fetch latest data (as before)
        logging.info("Auto-buy: Fetching latest identified stocks, positions, and holdings...")
        fetch_stocks(stock_data, positions_symbols)
        _, _, _, current_pos_symbols = process_positions()
        _, current_hold_symbols = get_holdings_internal()

        # 2. Filter identified stocks (Modified Condition)
        stocks_to_auto_buy = []
        if not stock_data:
             logging.info("Auto-buy: No stocks identified currently.")
        else:
            logging.info(f"Auto-buy: Identified: {[s.get('symbol', 'N/A') for s in stock_data if isinstance(s, dict)]}, Positions: {current_pos_symbols}, Holdings: {current_hold_symbols}")
            seen_symbols = set()
            for stock in stock_data:
                # Ensure stock is a dict and has 'symbol'
                if not isinstance(stock, dict) or 'symbol' not in stock:
                    logging.warning(f"Auto-buy: Skipping invalid stock data item: {stock}")
                    continue

                base_symbol = stock['symbol']

                if base_symbol in seen_symbols:
                    logging.info(f"Auto-buy: Skipping duplicate symbol {base_symbol}.")
                    continue
                seen_symbols.add(base_symbol)

                if (base_symbol not in current_pos_symbols and
                    base_symbol not in current_hold_symbols and
                    base_symbol not in sold_today_symbols):
                    stocks_to_auto_buy.append(stock)
                elif base_symbol in sold_today_symbols:
                    logging.info(f"Auto-buy: Skipping {base_symbol} because it was sold today.")
                elif base_symbol in current_pos_symbols:
                     logging.info(f"Auto-buy: Skipping {base_symbol} because it's in current positions.")
                elif base_symbol in current_hold_symbols:
                     logging.info(f"Auto-buy: Skipping {base_symbol} because it's in current holdings.")


        # 3. Execute buys if any
        if not stocks_to_auto_buy:
            logging.info("Auto-buy: No new identified stocks to buy (all are already in positions, holdings, or were sold today).")
        else:
            investment_amount = config.get('investment_amount_per_stock', 500)
            logging.info(f"Auto-buy: Attempting to buy {len(stocks_to_auto_buy)} stocks: {[s['symbol'] for s in stocks_to_auto_buy]} with amount ~₹{investment_amount} each.")

            buy_result = _execute_buy(
                api_instance=api,
                stocks_to_buy=stocks_to_auto_buy,
                amount=investment_amount,
                buy_as_paper_trade=False, 
                context="Auto-Buy"
            )

            # --- Process Buy Results Safely ---
            success_count = 0
            fail_count = 0
            buy_results_list = buy_result.get('results', []) 

            if not isinstance(buy_results_list, list):
                logging.error(f"Auto-buy: Invalid buy results format received: {buy_results_list}")
            else:
                for result in buy_results_list:
                    if not isinstance(result, dict):
                         logging.error(f"Auto-buy: Skipping invalid result item: {result}")
                         fail_count += 1 
                         continue

                    order_response = result.get('order_response')

                    if isinstance(order_response, dict) and order_response.get('stat') == 'Ok' and not result.get('error'):
                         success_count += 1
                         logging.info(f"Auto-buy SUCCESS: {result.get('symbol')} - Qty: {result.get('quantity')}")
                    else:
                         fail_count += 1
                         error_msg = result.get('error') 
                         if not error_msg and isinstance(order_response, dict):
                             error_msg = order_response.get('emsg', 'Unknown API error') 
                         elif not error_msg:
                             error_msg = 'Unknown failure (e.g., order response missing/None or not Ok)' 

                         logging.error(f"Auto-buy FAILED: {result.get('symbol')} - Error: {error_msg}")

            logging.info(f"Auto-buy job finished. Successful buys: {success_count}, Failed buys: {fail_count}.")

            if success_count > 0:
                 process_positions()
                 get_holdings_internal()

    except Exception as e:
        logging.error(f"Exception in auto_buy_identified_stocks task: {e}", exc_info=True)

    logging.info("--- Finished Auto-Buy Job ---")


@app.route('/api/trailing_logs')
def get_trailing_logs():
    return jsonify(trailing_logs)

@app.route('/api/clear_trailing_logs', methods=['POST'])
def clear_trailing_logs():
    global trailing_logs
    trailing_logs = []
    logging.info("Trailing logs cleared via API.")
    return jsonify({'status': 'success'})

@app.route('/api/clear_trailing_stop', methods=['POST'])
def clear_trailing_stop():
    if sell_holding.clear_trailing_stop_state():
        logging.info("Trailing stop data cleared successfully via API.")
        return jsonify({'status': 'success', 'message': 'Trailing stop data cleared successfully.'})
    else:
        logging.error("Failed to clear trailing stop data via API.")
        return jsonify({'status': 'error', 'message': 'Failed to clear trailing stop data.'}), 500

@app.route('/papertrade')
def papertrade():
    paper_trades = ptm.load_paper_trades()
    if login_successful:
        for trade in paper_trades:
            try:
                trading_symbol = getSymbolNameFinvasia(api, trade['symbol'])
                if trading_symbol:
                    current_price = fetch_current_price(api, trading_symbol)
                    trade['current_price'] = current_price
                else:
                    trade['current_price'] = None
            except Exception as e:
                logging.warning(f"Error fetching paper trade price for {trade['symbol']}: {e}")
                trade['current_price'] = None
    else:
        for trade in paper_trades:
             trade['current_price'] = None

    return render_template('papertrade.html', paper_trades=paper_trades)

@app.route('/api/papertrade/sell', methods=['POST'])
def sell_papertrade():
    data = request.get_json()
    symbol = data.get('symbol')

    if not symbol:
         return jsonify({'status': 'error', 'message': 'Symbol is required'}), 400

    result = ptm.sell_paper_trade(symbol, api)
    return jsonify(result)

@app.route('/api/relogin', methods=['POST'])
def relogin():
    global login_successful
    logging.info("Attempting relogin via API...")
    if attempt_login():
        return jsonify({'status': 'success', 'message': 'Relogin successful!'})
    else:
        return jsonify({'status': 'error', 'message': 'Relogin failed.'}), 500

@app.route('/api/manual_sell', methods=['POST'])
def manual_sell():
    global login_successful, trade_history_data
    if not login_successful:
        return jsonify({'error': 'Login required'}), 401

    data = request.json
    symbol = data.get('symbol')

    if not symbol:
        return jsonify({'error': 'Symbol required'}), 400

    logging.info(f"Manual sell request received for: {symbol}")
    try:
        result = sell_holding.manual_sell(api, symbol)
        logging.info(f"Manual sell result for {symbol}: {result}")
        if result.get('status') == 'success':
             process_positions()
             get_holdings_internal()
             trade_history_data = load_trade_history() 
             logging.info(f"Reloaded trade_history_data after manual sell of {symbol}.")
        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in manual sell API endpoint for {symbol}: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error during manual sell: {e}"}), 500

@app.route('/api/trailing_stop_active')


@app.route('/get_auto_pilot_status', methods=['GET'])
def get_auto_pilot_status():
    if not login_successful:
        return jsonify({'error': 'User not logged in. Cannot fetch Auto-Pilot status.'}), 401
    try:
        config = load_config()
        return jsonify({'auto_pilot_enabled': config.get('auto_pilot_enabled', False)})
    except Exception as e:
        logging.error(f"Error fetching Auto-Pilot status: {e}", exc_info=True)
        return jsonify({'error': 'Failed to fetch Auto-Pilot status'}), 500

@app.route('/set_auto_pilot_status', methods=['POST'])
def set_auto_pilot_status():
    if not login_successful:
        return jsonify({'error': 'User not logged in. Cannot set Auto-Pilot status.'}), 401
    try:
        data = request.get_json()
        is_enabled = data.get('auto_pilot_enabled')
        if is_enabled is None or not isinstance(is_enabled, bool):
            return jsonify({'error': 'Invalid payload. auto_pilot_enabled (boolean) is required.'}), 400

        config = load_config()
        config['auto_pilot_enabled'] = is_enabled
        save_config(config)
        logging.info(f"Auto-Pilot mode set to: {is_enabled}")
        return jsonify({'message': f'Auto-Pilot mode set to {is_enabled}', 'auto_pilot_enabled': is_enabled})
    except Exception as e:
        logging.error(f"Error setting Auto-Pilot status: {e}", exc_info=True)
        return jsonify({'error': 'Failed to set Auto-Pilot status'}), 500
def get_trailing_stop_active():
    """Returns a list of TRADING symbols currently being actively trailed."""
    try:
        state = sell_holding.load_trailing_stop_state()
        active_symbols = [symbol for symbol, data in state.items() if data.get('activated', False)]
        return jsonify(active_symbols)
    except Exception as e:
         logging.error(f"Error getting active trailing symbols: {e}", exc_info=True)
         return jsonify([])

@app.route('/api/buy_on_dip_state')
def get_buy_on_dip_state_api():
    """API endpoint to get the current buy_on_dip_state.json."""
    try:
        state = load_buy_on_dip_state() # This function already handles file not found
        return jsonify(state)
    except Exception as e:
        logging.error(f"API Error fetching buy_on_dip_state: {e}", exc_info=True)
        return jsonify({'error': f"Internal server error fetching buy_on_dip_state: {e}"}), 500

if __name__ == '__main__':
    logging.info("Starting Flask application...")
    app.run(host='0.0.0.0', port=5004, debug=False, use_reloader=False)