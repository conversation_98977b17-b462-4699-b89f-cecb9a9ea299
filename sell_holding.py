import json
import os
from order_management import *
from utils import load_config  # Changed from app to utils
from trade_history import update_trade_history_sell
from flask import jsonify
from telegram_notifications import send_telegram_message

TRAILING_STOP_FILE = 'TEMP/trailing_stop_loss.json'

def manual_sell(api, symbol):
    holdings_response = api.get_holdings()
    sold_quantity = 0

    for holding in holdings_response:
        if holding.get('stat') == 'Ok':
            for tsym_info in holding.get('exch_tsym', []):
                if tsym_info['exch'] == 'NSE' and tsym_info['tsym'] == symbol:
                    quantity = int(holding.get('holdqty', 0))
                    current_price = getCurrentPriceBySymbolName(api, symbol)

                    if quantity > 0 and current_price:
                        order_response = placeOrder(api, buy_or_sell='S',
                                                    tradingsymbol=symbol,
                                                    quantity=quantity,
                                                    price=current_price)
                        if order_response and order_response.get('stat') == 'Ok':
                            update_trade_history_sell(symbol, quantity, current_price, "Manual Sell")  # Add sell context
                            remove_symbol_from_trailing_stop(symbol)  # Clear trailing stop state
                            sold_quantity = quantity
                            return {'status': 'success',
                                    'message': f'Sold {quantity} shares of {symbol}'}

    return {'status': 'error', 'message': 'No holdings found for symbol'}

def load_trailing_stop_state():
    try:
        with open(TRAILING_STOP_FILE, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_trailing_stop_state(state):
    try:
        os.makedirs(os.path.dirname(TRAILING_STOP_FILE), exist_ok=True)
        with open(TRAILING_STOP_FILE, 'w') as f:
            json.dump(state, f)
    except Exception as e:
        print(f"Error saving trailing stop state: {e}")

def clear_trailing_stop_state():
    try:
        os.remove(TRAILING_STOP_FILE)
        print("Trailing stop data cleared successfully.")
        return True
    except FileNotFoundError:
        #print("Trailing stop file not found.")
        return True
    except Exception as e:
        print(f"Error clearing trailing stop data: {e}")
        return False

def remove_symbol_from_trailing_stop(symbol):
    state = load_trailing_stop_state()
    if symbol in state:
        del state[symbol]
        save_trailing_stop_state(state)
        print(f"Removed {symbol} from trailing stop state.")
    else:
        print(f"{symbol} not found in trailing stop state.")

# Initialize a set to keep track of sold trading symbols in this run
sold_symbols = set()

def sell_holding(api, logger=print):
    config = load_config()
    if not config.get('auto_sell_enabled', False):
        logger("Auto sell holding is disabled in config.")
        return

    dont_sell_symbols = config['symbols']
    sell_percentage = config.get('sell_percentage', 2.0)
    trailing_stop_loss_percentage = config.get('trailing_stop_loss_percentage', 0.5)

    # Load trailing_stop_state only if auto_sell is enabled and proceeding
    trailing_stop_state = load_trailing_stop_state()

    holdings_response = api.get_holdings()

    if holdings_response is None or not isinstance(holdings_response, list):
        return

    for holding in holdings_response:
        try:
            if holding['stat'] == 'Ok':
                holdqty = int(holding.get('holdqty', '0'))
                usedqty = int(holding.get('usedqty', '0'))

                if holdqty == 0 or usedqty > 0:
                    continue

                tradingsymbol = None
                for tsym_info in holding.get('exch_tsym', []):
                    if tsym_info['exch'] == 'NSE':
                        tradingsymbol = tsym_info['tsym']
                        break

                if not tradingsymbol:
                    continue

                if tradingsymbol in dont_sell_symbols:
                    print(f"Skipping {tradingsymbol} (marked as don't sell)")
                    continue

                if tradingsymbol in sold_symbols:
                    continue

                quantity = holdqty

                try:
                    averageBuyPrice = float(holding.get('upldprc', 0.0))
                except ValueError:
                    logger(f"Invalid average buy price for {tradingsymbol}")
                    continue

                try:
                    currentPrice = getCurrentPriceBySymbolName(api, tradingsymbol)
                    currentPrice = float(currentPrice)
                except (ValueError, TypeError):
                    logger(f"Invalid current price for {tradingsymbol}")
                    continue

                if currentPrice <= 0:
                    logger(f"Invalid current price for {tradingsymbol}")
                    continue

                if averageBuyPrice <= 0:
                    logger(f"Invalid average buy price for {tradingsymbol}")
                    continue

                state = trailing_stop_state.get(tradingsymbol,
                                                {'activated': False, 'highest_price': 0.0, 'stop_loss': 0.0})

                if not state['activated']:
                    if currentPrice >= averageBuyPrice * (1 + sell_percentage / 100):
                        state = {
                            'activated': True,
                            'highest_price': currentPrice,
                            'stop_loss': currentPrice * (1 - trailing_stop_loss_percentage / 100)
                        }
                        trailing_stop_state[tradingsymbol] = state
                        logger(f"Trailing activated: {tradingsymbol} @ {currentPrice}, Stop Loss: {state['stop_loss']:.2f}")

                if 'activated' in state and state['activated']:
                    # Update highest price only if current price is higher
                    if currentPrice > state['highest_price']:
                        state['highest_price'] = currentPrice

                        # Calculate new stop-loss only when highest price increases
                        new_stop_loss = state['highest_price'] * (1 - trailing_stop_loss_percentage / 100)

                        if new_stop_loss > state['stop_loss']:
                            state['stop_loss'] = new_stop_loss
                            logger(f"Updated SL for {tradingsymbol}: {new_stop_loss:.2f}")
                            log_message = f"{tradingsymbol} Trailing Updates:\n" \
                                          f"  Current Price: {currentPrice:.2f}\n" \
                                          f"  Stop Loss: {state['stop_loss']:.2f}\n" \
                                          f"  High: {state['highest_price']:.2f}"
                            logger(log_message)

                    if currentPrice < state['stop_loss']:
                        try:
                            order_response = placeOrder(api, buy_or_sell='S', tradingsymbol=tradingsymbol,
                                                        quantity=quantity, price=currentPrice)
                            logger(f"Sold {tradingsymbol} @ {currentPrice}: {order_response.get('stat', 'Unknown')}")
                            if order_response and isinstance(order_response, dict) and order_response.get('stat') == 'Ok':
                                sold_symbols.add(tradingsymbol)
                                update_trade_history_sell(tradingsymbol, quantity, currentPrice, "Auto Sell")
                                remove_symbol_from_trailing_stop(tradingsymbol)  # Clear trailing stop state for auto-sold symbol
                                logger(f"Successfully sold {quantity} of {tradingsymbol} and updated the Trading History")
                                # Send Telegram notification for successful auto-sell
                                try:
                                    sell_reason = "Stop-Loss Hit"
                                    message = f"⛔️ [Auto-Sell] SELL EXECUTED: {quantity} shares of {tradingsymbol} at ~₹{currentPrice:.2f} each. Reason: {sell_reason}."
                                    send_telegram_message(message)
                                    logger(f"Telegram notification sent for auto-sell of {tradingsymbol}.")
                                except Exception as e:
                                    logger(f"Error sending Telegram notification for auto-sell of {tradingsymbol}: {e}")
                            else:
                                logger(f"Failed to sell {tradingsymbol}")
                        except Exception as e:
                            logger(f"Error selling {tradingsymbol}: {str(e)}")

                trailing_stop_state[tradingsymbol] = state
        except Exception as e:
            logger(f"Error processing holding: {str(e)}")

    save_trailing_stop_state(trailing_stop_state)