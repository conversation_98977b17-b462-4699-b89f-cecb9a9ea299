import json
import os
import logging
from datetime import datetime

from order_management import placeOrder, getSymbolNameFinvasia, getCurrentPriceBySymbolName
from telegram_notifications import send_telegram_message
from utils import load_config 

JSON_DIR = os.path.join(os.path.dirname(__file__), 'json') 
BUY_ON_DIP_STATE_FILE = os.path.join(JSON_DIR, 'buy_on_dip_state.json')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def load_buy_on_dip_state():
    """Loads the state of buy on dip actions from a JSON file."""
    try:
        ensure_json_dir_exists()
        with open(BUY_ON_DIP_STATE_FILE, 'r') as f:
            state = json.load(f)
            for symbol_data in state.values():
                if 'triggered_thresholds' not in symbol_data:
                    symbol_data['triggered_thresholds'] = []
            return state
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_buy_on_dip_state(state):
    """Saves the state of buy on dip actions to a JSON file."""
    try:
        ensure_json_dir_exists()
        with open(BUY_ON_DIP_STATE_FILE, 'w') as f:
            json.dump(state, f, indent=4)
    except Exception as e:
        logging.error(f"Error saving buy on dip state: {e}")

def ensure_json_dir_exists():
    """Creates the JSON directory if it doesn't exist."""
    if not os.path.exists(JSON_DIR):
        try:
            os.makedirs(JSON_DIR)
            logging.info(f"Created directory: {JSON_DIR}")
        except OSError as e:
            logging.error(f"Error creating directory {JSON_DIR}: {e}")

def remove_symbol_from_buy_on_dip_state(symbol):
    """Removes a symbol's data from the buy_on_dip_state.json file."""
    state = load_buy_on_dip_state()
    if symbol in state:
        del state[symbol]
        save_buy_on_dip_state(state)
        logging.info(f"Removed {symbol} from buy on dip state after selling.")
    else:
        logging.info(f"{symbol} not found in buy on dip state, no removal needed.")

def execute_buy_on_dip_logic(api_instance):
    """Main logic to check holdings and execute buy orders based on dip thresholds."""
    config = load_config()
    if not config.get('buy_on_dip_enabled', False):
        # logging.info("Buy on dip feature is disabled in config.")
        return

    buy_on_dip_config = config.get('buy_on_dip_config', [])
    # Sort by threshold descending to check higher dips first
    buy_on_dip_config = sorted(buy_on_dip_config, key=lambda x: x.get('threshold', 0), reverse=True)

    if not buy_on_dip_config:
        logging.warning("No buy on dip thresholds configured.")
        return

    state = load_buy_on_dip_state()
    # Daily reset logic removed as per requirement

    try:
        holdings_response = api_instance.get_holdings()
        if not holdings_response:
            logging.info("No holdings data received from API.")
            return
    except Exception as e:
        logging.error(f"Error fetching holdings: {e}", exc_info=True)
        return

    logging.info(f"Buy on Dip: Checking {len(holdings_response)} holdings.")

    for holding in holdings_response:
        if not isinstance(holding, dict) or holding.get('stat') != 'Ok':
            continue

        try:
            # Determine the correct trading symbol (NSE preference)
            trading_symbol = None
            holding_tysm = holding.get('tsym') # Used if exch_tsym is not present or not NSE
            stock_name = holding.get('stkname', '') # Get stock name if available

            if 'exch_tsym' in holding and isinstance(holding['exch_tsym'], list):
                for tsym_info in holding['exch_tsym']:
                    if isinstance(tsym_info, dict) and tsym_info.get('exch') == 'NSE':
                        trading_symbol = tsym_info.get('tsym')
                        if not stock_name and 'token' in tsym_info: # Attempt to get name from token if not already set
                            # This part is tricky as 'stkname' might not be in 'exch_tsym'
                            # We'll rely on the top-level 'stkname' or use symbol as name
                            pass
                        break
                if not trading_symbol and holding_tysm: # Fallback to top-level tsym if NSE not found
                    trading_symbol = holding_tysm
            elif holding_tysm:
                trading_symbol = holding_tysm
            
            if not trading_symbol:
                logging.warning(f"Could not determine trading symbol for holding: {holding.get('tkn', 'Unknown Token')}")
                continue
            
            if not stock_name:
                stock_name = trading_symbol.split('-')[0] 

            current_quantity = int(holding.get('holdqty', 0))
            if current_quantity <= 0:
                continue

            avg_buy_price_str = holding.get('upldprc') 
            if not avg_buy_price_str:
                logging.warning(f"Missing average buy price (upldprc) for {trading_symbol}. Skipping.")
                continue
            avg_buy_price = float(avg_buy_price_str)
            if avg_buy_price <= 0:
                logging.warning(f"Invalid average buy price {avg_buy_price} for {trading_symbol}. Skipping.")
                continue

            current_price_str = getCurrentPriceBySymbolName(api_instance, trading_symbol)
            if not current_price_str:
                logging.warning(f"Could not fetch current price for {trading_symbol}. Skipping dip check.")
                continue
            current_price = float(current_price_str)

            loss_percentage = ((avg_buy_price - current_price) / avg_buy_price) * 100

            if trading_symbol not in state:
                state[trading_symbol] = {'triggered_thresholds': []}
            
            if 'triggered_thresholds' not in state[trading_symbol]:
                 state[trading_symbol]['triggered_thresholds'] = []

            if loss_percentage > 0: 
                logging.info(f"Stock: {trading_symbol}, AvgBuy: {avg_buy_price:.2f}, CurrPrice: {current_price:.2f}, Loss: {loss_percentage:.2f}%")
                for dip_config_item in buy_on_dip_config:
                    threshold = dip_config_item.get('threshold')
                    buy_quantity_percentage_for_threshold = dip_config_item.get('percentage', 0) / 100.0

                    if threshold is None or buy_quantity_percentage_for_threshold <= 0:
                        logging.warning(f"Invalid dip config item for {trading_symbol}: {dip_config_item}. Skipping.")
                        continue

                    if loss_percentage >= threshold:
                        if threshold not in state[trading_symbol]['triggered_thresholds']:
                            qty_to_buy = int(current_quantity * buy_quantity_percentage_for_threshold)
                            if qty_to_buy < 1:
                                logging.info(f"Calculated quantity to buy for {trading_symbol} is less than 1 ({qty_to_buy}). Skipping buy.")
                                state[trading_symbol]['triggered_thresholds'].append(threshold)
                                break 

                            logging.info(f"{trading_symbol} crossed {threshold}% loss threshold (Loss: {loss_percentage:.2f}%). Current holding: {current_quantity}. Attempting to buy {qty_to_buy} shares.")
                            
                            order_response = placeOrder(api_instance, 
                                                        buy_or_sell='B', 
                                                        tradingsymbol=trading_symbol, 
                                                        quantity=qty_to_buy, 
                                                        price=current_price)

                            if order_response and order_response.get('stat') == 'Ok':
                                logging.info(f"Successfully placed buy order for {qty_to_buy} of {trading_symbol} at ~{current_price:.2f}. Order ID: {order_response.get('norenordno')}")
                                state[trading_symbol]['triggered_thresholds'].append(threshold)
                                from trade_history import update_trade_history_buy # Moved import here
                                update_trade_history_buy(
                                    symbol=trading_symbol, 
                                    name=stock_name, 
                                    quantity=qty_to_buy, 
                                    price=current_price,
                                    context="Buy on Dip"  # Pass the buy context
                                )
                                # Send Telegram notification for successful Buy on Dip
                                try:
                                    message = f"✅ [Buy on Dip] BUY EXECUTED: {qty_to_buy} shares of {trading_symbol} at ~₹{current_price:.2f} each. (Loss Threshold: {threshold}%)"
                                    send_telegram_message(message)
                                    logging.info(f"Telegram notification sent for Buy on Dip of {trading_symbol}.")
                                except Exception as e:
                                    logging.error(f"Error sending Telegram notification for Buy on Dip of {trading_symbol}: {e}", exc_info=True)
                            else:
                                error_msg = order_response.get('emsg', 'Unknown error') if order_response else 'Order placement failed'
                                logging.error(f"Failed to place buy order for {trading_symbol}: {error_msg}")
                            
                            break 

        except Exception as e:
            logging.error(f"Error processing holding {holding.get('tsym', 'N/A')} for buy on dip: {e}", exc_info=True)
            continue 

    save_buy_on_dip_state(state)
    logging.info("Buy on dip check completed.")
